/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:28:41 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.PipedWriter;
import java.io.StringReader;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import javax.sql.rowset.RowSetMetaDataImpl;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.DuplicateHeaderMode;
import org.apache.commons.csv.QuoteMode;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockFileWriter;
import org.evosuite.runtime.mock.java.io.MockPrintStream;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVFormat_ESTest extends CSVFormat_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      Object[] objectArray0 = new Object[8];
      objectArray0[3] = (Object) "c)d2bI%P";
      String string0 = cSVFormat0.format(objectArray0);
      assertEquals(",,,c)d2bI%P,,,,", string0);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('+');
      Object[] objectArray0 = new Object[3];
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      cSVFormat1.hashCode();
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertEquals('+', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withTrim(false);
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(true);
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      assertTrue(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      assertTrue(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('');
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("ALL_NON_NULL");
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals('', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withSystemRecordSeparator();
      assertEquals("\n", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(false);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("reader");
      assertFalse(cSVFormat1.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("appendable");
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('e');
      assertEquals("e", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      QuoteMode quoteMode0 = QuoteMode.NON_NUMERIC;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withQuote('d');
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertEquals('d', (char)cSVFormat2.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withQuote('x');
      assertEquals('x', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withQuote('d');
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertEquals('d', (char)cSVFormat2.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withIgnoreSurroundingSpaces(true);
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertEquals("", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getTrim());
      assertTrue(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('');
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("ALL_NON_NULL");
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat0.isNullStringSet());
      assertEquals('', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertEquals('', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      Character character0 = new Character('Y');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      Character character1 = Character.valueOf('\\');
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker(character1);
      CSVFormat cSVFormat3 = cSVFormat2.withTrailingDelimiter();
      CSVFormat cSVFormat4 = cSVFormat3.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat4.getIgnoreHeaderCase());
      assertTrue(cSVFormat4.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      CSVFormat cSVFormat3 = cSVFormat2.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertTrue(cSVFormat3.getTrim());
      assertTrue(cSVFormat3.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator(";!s!u5]G~6%JObY+N");
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.DEFAULT.withIgnoreHeaderCase();
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(true);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrim());
      assertEquals("", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(false);
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      Object[] objectArray0 = new Object[5];
      CSVFormat cSVFormat2 = cSVFormat1.withHeaderComments(objectArray0);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      Object[] objectArray0 = new Object[2];
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Object[] objectArray0 = new Object[0];
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withHeader((String[]) null);
      assertNotSame(cSVFormat0, cSVFormat1);
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      String[] stringArray0 = new String[2];
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(stringArray0);
      assertTrue(cSVFormat1.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('');
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("ALL_NON_NULL");
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals("", cSVFormat2.getDelimiterString());
      assertFalse(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('');
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertEquals("", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(resultSet0);
      assertEquals("\n", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSet) null);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      Character character0 = new Character('#');
      CSVFormat cSVFormat1 = cSVFormat0.DEFAULT.withEscape(character0);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Character character0 = Character.valueOf('#');
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withEscape(character0);
      assertEquals('\"', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withEscape('^');
      assertEquals('^', (char)cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withEscape('z');
      assertEquals('z', (char)cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('#');
      assertEquals("#", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('(');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('(');
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('(');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('(');
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Default;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withCommentMarker('.');
      assertEquals('.', (char)cSVFormat1.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD_CSV.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('g');
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertEquals('g', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('');
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(false);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertTrue(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertEquals("", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withAutoFlush(false);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withAllowMissingColumnNames(true);
      assertTrue(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      Character character0 = Character.valueOf('e');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withAllowDuplicateHeaderNames(true);
      assertTrue(cSVFormat1.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('(');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(false);
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertEquals("(", cSVFormat2.getDelimiterString());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withAllowDuplicateHeaderNames();
      assertNotSame(cSVFormat0, cSVFormat1);
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      Character character0 = new Character('Y');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      Character character1 = Character.valueOf('\\');
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker(character1);
      CSVFormat cSVFormat3 = cSVFormat2.withTrailingDelimiter();
      CSVFormat cSVFormat4 = cSVFormat3.withAllowDuplicateHeaderNames();
      assertTrue(cSVFormat4.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withAllowDuplicateHeaderNames();
      assertTrue(cSVFormat1.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      String string0 = cSVFormat0.trim("");
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      String[] stringArray0 = CSVFormat.toStringArray((Object[]) null);
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      MockFile mockFile0 = new MockFile(">)3pS:>-nrAes .V$P");
      Charset charset0 = Charset.defaultCharset();
      CSVPrinter cSVPrinter0 = cSVFormat0.POSTGRESQL_CSV.print((File) mockFile0, charset0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines(true);
      cSVFormat2.getQuoteMode();
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Character character0 = new Character('D');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      Character character1 = cSVFormat1.getEscapeCharacter();
      assertEquals('D', (char)character1);
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertFalse(cSVFormat1.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.copy();
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      PipedWriter pipedWriter0 = new PipedWriter();
      try { 
        cSVFormat0.POSTGRESQL_TEXT.println(pipedWriter0);
        fail("Expecting exception: IOException");
      
      } catch(IOException e) {
         //
         // Pipe not connected
         //
         verifyException("java.io.PipedWriter", e);
      }
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      MockFile mockFile0 = new MockFile("The comment start character and the delimiter cannot be the same ('");
      Path path0 = mockFile0.toPath();
      // Undeclared exception!
      try { 
        cSVFormat0.print(path0, (Charset) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.nio.file.Files", e);
      }
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      File file0 = MockFile.createTempFile("org.apache.commons.csv.DuplicateHeaderMode", "X;5!s0Kf}S1*f.)|qb3");
      MockFile mockFile0 = new MockFile(file0, "X;5!s0Kf}S1*f.)|qb3");
      try { 
        cSVFormat0.print((File) mockFile0, (Charset) null);
        fail("Expecting exception: FileNotFoundException");
      
      } catch(FileNotFoundException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockFileOutputStream", e);
      }
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('e');
      Character character0 = new Character('e');
      // Undeclared exception!
      try { 
        cSVFormat1.withEscape(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The escape character and the delimiter cannot be the same ('e')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      String string0 = cSVFormat0.ORACLE.toString();
      assertEquals("Delimiter=<,> Escape=<\\> QuoteChar=<\"> QuoteMode=<MINIMAL> NullString=<\\N> RecordSeparator=<\n> SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      String string0 = cSVFormat1.toString();
      assertEquals("Delimiter=<,> QuoteChar=<\"> QuoteMode=<ALL> RecordSeparator=<\r\n> SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      MockPrintStream mockPrintStream0 = new MockPrintStream("c)d2bI%P");
      cSVFormat0.println(mockPrintStream0);
      assertFalse(cSVFormat0.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      MockPrintStream mockPrintStream0 = new MockPrintStream("c)d2bI%P");
      cSVFormat0.print((Object) cSVFormat0, (Appendable) mockPrintStream0, true);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      boolean boolean0 = cSVFormat0.isEscapeCharacterSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.isEscapeCharacterSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      String[] stringArray0 = cSVFormat0.getHeader();
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      Character character0 = Character.valueOf('x');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote(character0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(resultSet0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(class0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = new Character('q');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape(character0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter("+2fbb[c");
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Character character0 = Character.valueOf('$');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker(character0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setNullString("");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setSkipHeaderRecord(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      cSVFormat0.getQuoteMode();
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrim(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((String[]) null);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      String string0 = cSVFormat0.getDelimiterString();
      assertEquals("\t", string0);
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAllowMissingColumnNames(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('');
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      cSVFormat_Builder0.setAutoFlush(true);
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.isNullStringSet());
      assertEquals('', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      Character character0 = cSVFormat0.getCommentMarker();
      assertNull(character0);
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreHeaderCase(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreEmptyLines(true);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrailingDelimiter(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.getTrailingDelimiter();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      boolean boolean0 = cSVFormat0.getAutoFlush();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      QuoteMode quoteMode0 = QuoteMode.NON_NUMERIC;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuoteMode(quoteMode0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeaderComments((Object[]) null);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      boolean boolean0 = cSVFormat0.getTrim();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator("k$p");
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('+');
      boolean boolean0 = cSVFormat0.getIgnoreSurroundingSpaces();
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertEquals("+", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Character character0 = cSVFormat0.getEscapeCharacter();
      assertNull(character0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withAllowDuplicateHeaderNames(false);
      assertFalse(cSVFormat1.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('+');
      QuoteMode quoteMode0 = QuoteMode.NONE;
      // Undeclared exception!
      try { 
        cSVFormat0.withQuoteMode(quoteMode0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No quotes mode set but no escape character is set
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Character character0 = Character.valueOf('<');
      CSVFormat cSVFormat1 = cSVFormat0.withQuote(character0);
      // Undeclared exception!
      try { 
        cSVFormat1.withCommentMarker('<');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the quoteChar cannot be the same ('<')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      String string0 = cSVFormat0.INFORMIX_UNLOAD_CSV.trim("e*");
      assertEquals("e*", string0);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Class<CSVFormat.Predefined> class0 = CSVFormat.Predefined.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      String string0 = cSVFormat1.toString();
      assertEquals("Delimiter=<|> Escape=<\\> QuoteChar=<\"> RecordSeparator=<\n> EmptyLines:ignored SkipHeaderRecord:false Header:[Default, Excel, InformixUnload, InformixUnloadCsv, MongoDBCsv, MongoDBTsv, MySQL, Oracle, PostgreSQLCsv, PostgreSQLText, RFC4180, TDF]", string0);
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      Object[] objectArray0 = new Object[6];
      objectArray0[0] = (Object) cSVFormat0;
      String[] stringArray0 = CSVFormat.toStringArray(objectArray0);
      assertEquals(6, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('(');
      Object[] objectArray0 = new Object[8];
      objectArray0[0] = (Object) cSVFormat0;
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.isCommentMarkerSet());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertEquals('(', cSVFormat1.getDelimiter());
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      String[] stringArray0 = new String[6];
      stringArray0[1] = " IgnoreHeaderCase:ignored";
      String string0 = cSVFormat0.format(stringArray0);
      assertTrue(cSVFormat0.isEscapeCharacterSet());
      assertEquals("\t\" IgnoreHeaderCase:ignored\"\t\t\t\t", string0);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      String[] stringArray0 = new String[6];
      stringArray0[2] = "";
      String string0 = cSVFormat0.format(stringArray0);
      assertTrue(cSVFormat0.isEscapeCharacterSet());
      assertTrue(cSVFormat0.isQuoteCharacterSet());
      assertEquals("\t\t\t\t\t", string0);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn(rowSetMetaDataImpl0).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withHeader(resultSet0);
      assertFalse(cSVFormat1.isEscapeCharacterSet());
      
      Object[] objectArray0 = new Object[8];
      objectArray0[0] = (Object) cSVFormat0;
      cSVFormat1.format(objectArray0);
      assertTrue(cSVFormat1.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      assertFalse(cSVFormat0.isQuoteCharacterSet());
      
      Object[] objectArray0 = new Object[2];
      Object object0 = new Object();
      objectArray0[0] = object0;
      cSVFormat0.format(objectArray0);
      assertTrue(cSVFormat0.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('');
      boolean boolean0 = cSVFormat0.getAllowDuplicateHeaderNames();
      assertTrue(boolean0);
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertEquals('', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      // Undeclared exception!
      try { 
        cSVFormat0.format((Object[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      Object object0 = new Object();
      boolean boolean0 = cSVFormat0.equals(object0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      boolean boolean0 = cSVFormat1.equals(cSVFormat0);
      assertFalse(cSVFormat0.equals((Object)cSVFormat1));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Object[] objectArray0 = new Object[9];
      String string0 = cSVFormat0.format(objectArray0);
      assertEquals("\\N,\\N,\\N,\\N,\\N,\\N,\\N,\\N,\\N", string0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      // Undeclared exception!
      try { 
        CSVFormat.trim((CharSequence) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withQuote((Character) null);
      assertNull(cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("}!u^7;C..6;8");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      // Undeclared exception!
      try { 
        cSVFormat_Builder0.setDelimiter("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The delimiter cannot be empty
         //
         verifyException("org.apache.commons.csv.CSVFormat$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("+p&ZXpB");
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      Object[] objectArray0 = new Object[2];
      objectArray0[0] = (Object) cSVFormat0;
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertTrue(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.isQuoteCharacterSet());
      assertTrue(cSVFormat1.isEscapeCharacterSet());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withSystemRecordSeparator();
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      QuoteMode quoteMode0 = QuoteMode.NON_NUMERIC;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((String[]) null);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      DuplicateHeaderMode duplicateHeaderMode0 = cSVFormat0.getDuplicateHeaderMode();
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, duplicateHeaderMode0);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(false);
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      // Undeclared exception!
      try { 
        CSVFormat.valueOf("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.csv.CSVFormat.Predefined.
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('(');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      Object[] objectArray0 = new Object[8];
      objectArray0[0] = (Object) cSVFormat1;
      CSVFormat cSVFormat2 = cSVFormat1.withHeaderComments(objectArray0);
      assertFalse(cSVFormat2.isQuoteCharacterSet());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.isCommentMarkerSet());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals('(', cSVFormat2.getDelimiter());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat2.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      MockFileWriter mockFileWriter0 = new MockFileWriter("e*");
      CSVPrinter cSVPrinter0 = cSVFormat0.INFORMIX_UNLOAD_CSV.print((Appendable) mockFileWriter0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('G');
      assertEquals("G", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(false);
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withTrim();
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      StringReader stringReader0 = new StringReader("");
      CSVParser cSVParser0 = cSVFormat0.INFORMIX_UNLOAD_CSV.parse(stringReader0);
      assertNull(cSVParser0.getTrailerComment());
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(resultSet0);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('F');
      String string0 = cSVFormat1.toString();
      assertEquals("Delimiter=<,> QuoteChar=<\"> CommentStart=<F> RecordSeparator=<\r\n> SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      String string0 = cSVFormat0.getRecordSeparator();
      assertEquals("\n", string0);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVPrinter cSVPrinter0 = cSVFormat0.TDF.printer();
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      Object[] objectArray0 = new Object[7];
      MockFileWriter mockFileWriter0 = new MockFileWriter("e*");
      cSVFormat0.printRecord(mockFileWriter0, objectArray0);
      assertNull(cSVFormat0.getNullString());
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      boolean boolean0 = cSVFormat0.getSkipHeaderRecord();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      DuplicateHeaderMode duplicateHeaderMode0 = DuplicateHeaderMode.DISALLOW;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDuplicateHeaderMode(duplicateHeaderMode0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator('*');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("l");
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker('z');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote('@');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreSurroundingSpaces(true);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAutoFlush(true);
      assertTrue(cSVFormat2.getAutoFlush());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("");
      assertNull(cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      boolean boolean0 = cSVFormat1.getSkipHeaderRecord();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      String[] stringArray0 = new String[1];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeaderComments(stringArray0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter('-');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      QuoteMode quoteMode0 = QuoteMode.MINIMAL;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      assertNotSame(cSVFormat1, cSVFormat0);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape('*');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }
}
