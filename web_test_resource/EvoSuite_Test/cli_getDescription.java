/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:36:10 GMT 2025
 */

package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.cli.Option;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Option_ESTest extends Option_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setArgs((-731));
      boolean boolean0 = option0.requiresArg();
      assertEquals((-731), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      assertEquals((-1), option0.getArgs());
      
      option0.setArgs(0);
      option0.toString();
      assertEquals(0, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.required(true);
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.isRequired();
      assertEquals((-1), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.optionalArg(true);
      option_Builder1.longOpt("");
      Option option0 = option_Builder0.build();
      option0.hasOptionalArg();
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      option_Builder0.hasArgs();
      Option option0 = option_Builder0.build();
      option0.addValueForProcessing("4kJg");
      option0.getValuesList();
      assertFalse(option0.hasValueSeparator());
      assertTrue(option0.hasArg());
      assertTrue(option0.hasArgs());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, false, (String) null);
      option0.setValueSeparator('Y');
      char char0 = option0.getValueSeparator();
      assertEquals('Y', char0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.getValue((String) null);
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      option_Builder0.hasArg(true);
      Option option0 = option_Builder0.build();
      assertTrue(option0.hasArg());
      
      option0.addValueForProcessing("");
      String string0 = option0.getValue();
      assertFalse(option0.hasValueSeparator());
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.getOpt();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Option option0 = new Option("", "");
      String string0 = option0.getLongOpt();
      assertNull(string0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.longOpt("");
      Option option0 = option_Builder1.build();
      option0.getLongOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Option option0 = new Option("1", "1");
      option0.getKey();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      option0.getKey();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("l");
      Option option0 = option_Builder0.build();
      option0.getId();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("sP");
      Option option0 = option_Builder0.build();
      option0.getDescription();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Option option0 = new Option("", "");
      String string0 = option0.getDescription();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      assertEquals((-1), option0.getArgs());
      
      option0.setArgs(0);
      int int0 = option0.getArgs();
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setArgs(1928);
      int int0 = option0.getArgs();
      assertEquals(1928, int0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      option_Builder0.argName("4kJg");
      Option option0 = option_Builder0.build();
      option0.getArgName();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setArgName("");
      option0.getArgName();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.acceptsArg();
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Option option0 = new Option("", "");
      boolean boolean0 = option0.acceptsArg();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.addValueForProcessing("");
      try { 
        option0.getValue(1);
        fail("Expecting exception: IndexOutOfBoundsException");
      
      } catch(IndexOutOfBoundsException e) {
         //
         // Index: 1, Size: 1
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Option option0 = new Option((String) null, "oS1*,~S");
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      // Undeclared exception!
      try { 
        Option.builder("[ option: ");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '[ option: ' contains an illegal character : '['
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      option_Builder0.hasArgs();
      Option.Builder option_Builder1 = option_Builder0.valueSeparator();
      Option option0 = option_Builder1.build();
      // Undeclared exception!
      try { 
        option0.addValueForProcessing((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("]", true, "]");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal option name ']'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("|Yr5XTWW#E", "|Yr5XTWW#E", false, "|Yr5XTWW#E");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '|Yr5XTWW#E' contains an illegal character : '|'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("The option '", "The option '");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'The option '' contains an illegal character : ' '
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Option option0 = new Option("", "");
      boolean boolean0 = option0.hasValueSeparator();
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      option0.setValueSeparator('@');
      boolean boolean0 = option0.hasValueSeparator();
      assertEquals('@', option0.getValueSeparator());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Option option0 = new Option("", "");
      boolean boolean0 = option0.hasArgs();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Option option0 = new Option("", "/4S p");
      boolean boolean0 = option0.hasArg();
      assertEquals("/4S p", option0.getDescription());
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, false, (String) null);
      option0.setArgs(1928);
      boolean boolean0 = option0.hasArg();
      assertEquals(1928, option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.setArgs((-2));
      boolean boolean0 = option0.hasArg();
      assertEquals((-2), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.getValue();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Option option0 = new Option("", true, "L)");
      option0.addValueForProcessing("L)");
      String string0 = option0.getValue();
      assertFalse(option0.hasValueSeparator());
      assertEquals("L)", option0.getDescription());
      assertNotNull(string0);
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getValueSeparator();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("");
      Option option0 = option_Builder0.build();
      option0.hasOptionalArg();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.setType((Object) null);
      String string0 = option0.toString();
      assertEquals((-1), option0.getArgs());
      assertEquals("[ option: null  :: null ]", string0);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setArgs(559);
      option0.toString();
      assertEquals(559, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      Option option0 = new Option("", "IT-GV&bty", false, "");
      String string0 = option0.toString();
      assertEquals((-1), option0.getArgs());
      assertEquals("[ option:  IT-GV&bty  ::  :: class java.lang.String ]", string0);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      Option option0 = new Option("", true, "L)");
      String string0 = option0.toString();
      assertEquals("[ option:   [ARG] :: L) :: class java.lang.String ]", string0);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt(".S");
      Option.Builder option_Builder1 = option_Builder0.hasArgs();
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.requiresArg();
      assertTrue(option0.hasArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("x5");
      Option.Builder option_Builder1 = option_Builder0.longOpt("3'-%dcMilDMakl0WG=X");
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.hasLongOpt();
      assertTrue(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      Option option0 = new Option("", "");
      boolean boolean0 = option0.hasLongOpt();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setArgs(559);
      boolean boolean0 = option0.hasArgs();
      assertEquals(559, option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      option_Builder0.argName("");
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasArgName();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("1");
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasArgName();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt(".S");
      Option.Builder option_Builder1 = option_Builder0.hasArgs();
      Option option0 = option_Builder1.build();
      option0.addValueForProcessing("org.apache.commons.cli.Option");
      boolean boolean0 = option0.requiresArg();
      assertTrue(option0.hasArg());
      assertFalse(option0.hasValueSeparator());
      assertFalse(boolean0);
      assertEquals((-2), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      String[] stringArray0 = option0.getValues();
      assertEquals((-1), option0.getArgs());
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.addValueForProcessing("");
      String[] stringArray0 = option0.getValues();
      assertNotNull(stringArray0);
      assertFalse(option0.hasValueSeparator());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      Option option0 = new Option("W", "W");
      option0.getValue(34);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.addValueForProcessing("");
      try { 
        option0.getValue((-1023));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("");
      Option option0 = option_Builder0.build();
      String string0 = option0.getValue("");
      assertEquals((-1), option0.getArgs());
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.longOpt(".S");
      option_Builder1.numberOfArgs(559);
      Option option0 = option_Builder0.build();
      option0.addValueForProcessing("org.apache.commons.cli.Option");
      String string0 = option0.getValue("");
      assertEquals("org.apache.commons.cli.Option", string0);
      assertFalse(option0.hasValueSeparator());
      assertEquals(559, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, false, (String) null);
      option0.getKey();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      Option option1 = option_Builder0.build();
      boolean boolean0 = option0.equals(option1);
      assertEquals((-1), option1.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      Option option0 = new Option("", "");
      Option option1 = new Option("", "", true, "loW");
      boolean boolean0 = option0.equals(option1);
      assertEquals("", option1.getLongOpt());
      assertEquals("loW", option1.getDescription());
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      Option option0 = new Option("k", "k");
      Option option1 = new Option("", "");
      boolean boolean0 = option0.equals(option1);
      assertFalse(boolean0);
      assertEquals((-1), option1.getArgs());
      assertFalse(option1.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt(".S");
      Option option0 = option_Builder0.build();
      Object object0 = new Object();
      boolean boolean0 = option0.equals(object0);
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      Option option0 = new Option("1", "1");
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("1");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // NO_ARGS_ALLOWED
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.optionalArg(true);
      option_Builder1.numberOfArgs((-3617));
      option_Builder1.option("");
      Option option0 = option_Builder1.build();
      assertTrue(option0.hasOptionalArg());
      
      option0.addValueForProcessing(";m8`6p");
      assertFalse(option0.hasArg());
      assertFalse(option0.hasArgs());
      assertFalse(option0.hasValueSeparator());
      assertEquals((-3617), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test64()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.optionalArg(false);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test65()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.hasArg(false);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test66()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      // Undeclared exception!
      try { 
        option_Builder0.build();
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Either opt or longOpt must be specified
         //
         verifyException("org.apache.commons.cli.Option$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test67()  throws Throwable  {
      Option option0 = new Option("", "IT-GV&bty", false, "");
      assertEquals("IT-GV&bty", option0.getLongOpt());
      
      option0.setLongOpt("IT-GV&bty");
      assertEquals("", option0.getDescription());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test68()  throws Throwable  {
      Option option0 = new Option("f1", "E9G3>Z1P;{/");
      option0.getOpt();
      assertFalse(option0.hasLongOpt());
      assertEquals("E9G3>Z1P;{/", option0.getDescription());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test69()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt(".S");
      Option option0 = option_Builder0.build();
      option0.getType();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test70()  throws Throwable  {
      Option option0 = new Option("", "");
      // Undeclared exception!
      try { 
        option0.addValue("");
        fail("Expecting exception: UnsupportedOperationException");
      
      } catch(UnsupportedOperationException e) {
         //
         // The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. 
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test71()  throws Throwable  {
      Option option0 = new Option("", "IT-GV&bty", false, "");
      String string0 = option0.getLongOpt();
      assertEquals((-1), option0.getArgs());
      assertEquals("IT-GV&bty", string0);
      assertEquals("", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test72()  throws Throwable  {
      Option option0 = new Option("", "IT-GV&bty", false, "");
      option0.hashCode();
      assertEquals((-1), option0.getArgs());
      assertEquals("", option0.getDescription());
      assertEquals("IT-GV&bty", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test73()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      option0.isRequired();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test74()  throws Throwable  {
      Option option0 = new Option("", true, "");
      assertTrue(option0.hasArg());
      
      option0.addValueForProcessing("");
      boolean boolean0 = option0.requiresArg();
      assertFalse(boolean0);
      assertFalse(option0.hasValueSeparator());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test75()  throws Throwable  {
      Option option0 = new Option("", "/4S p");
      String string0 = option0.getDescription();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
      assertNotNull(string0);
      assertEquals("/4S p", string0);
  }

  @Test(timeout = 4000)
  public void test76()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("l");
      Option option0 = option_Builder0.build();
      int int0 = option0.getArgs();
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test77()  throws Throwable  {
      Option option0 = new Option("vO", "vO");
      option0.clearValues();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test78()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.valueSeparator('&');
      Option.Builder option_Builder2 = option_Builder1.longOpt(".S");
      option_Builder2.numberOfArgs(559);
      Option option0 = option_Builder2.build();
      option0.addValueForProcessing("org.apache.commons.cli.Option");
      assertTrue(option0.hasValueSeparator());
      assertEquals('&', option0.getValueSeparator());
      assertTrue(option0.hasArg());
      assertEquals(559, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test79()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.numberOfArgs((-3617));
      option_Builder0.option("");
      Option option0 = option_Builder0.build();
      // Undeclared exception!
      try { 
        option0.addValueForProcessing(";m8`6p");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot add value, list full.
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test80()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setDescription("");
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test81()  throws Throwable  {
      Option option0 = new Option("", "IT-GV&bty", false, "");
      Option option1 = (Option)option0.clone();
      assertNotSame(option1, option0);
      assertEquals((-1), option1.getArgs());
      assertEquals("IT-GV&bty", option1.getLongOpt());
      assertEquals("", option1.getDescription());
  }

  @Test(timeout = 4000)
  public void test82()  throws Throwable  {
      Option option0 = new Option("", "R*]]t0qY*o");
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test83()  throws Throwable  {
      Option option0 = new Option("", "IT-GV&bty", false, "");
      Class<Object> class0 = Object.class;
      option0.setType(class0);
      assertEquals((-1), option0.getArgs());
      assertEquals("", option0.getDescription());
      assertEquals("IT-GV&bty", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test84()  throws Throwable  {
      Option option0 = new Option("k", "k");
      option0.setRequired(true);
      assertTrue(option0.isRequired());
  }

  @Test(timeout = 4000)
  public void test85()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      option0.setOptionalArg(true);
      boolean boolean0 = option0.requiresArg();
      assertTrue(option0.hasOptionalArg());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test86()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.getArgName();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test87()  throws Throwable  {
      Option option0 = new Option("", "");
      // Undeclared exception!
      try { 
        option0.setType((Object) "");
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.String cannot be cast to java.lang.Class
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test88()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Class<Object> class0 = Object.class;
      Option.Builder option_Builder1 = option_Builder0.type(class0);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test89()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.hasArgs();
      option_Builder1.option("W");
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.hasArgs();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test90()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.hasArg();
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test91()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.desc((String) null);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test92()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.required();
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test93()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.argName(".S");
      option_Builder0.longOpt(".S");
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasArgName();
      assertEquals((-1), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test94()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt(".S");
      Option option0 = option_Builder0.build();
      option0.getValuesList();
      assertEquals((-1), option0.getArgs());
  }
}
