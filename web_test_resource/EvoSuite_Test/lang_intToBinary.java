/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:25:40 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.Conversion;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.util.MockUUID;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Conversion_ESTest extends Conversion_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte) (-31), (byte) (-31), "w6uc$", 4, 1);
      assertEquals("w6uc0", string0);
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      String string0 = Conversion.intToHex((byte) (-1), (byte)7, "Ld_KjX$*Z5ZK", 1, 1);
      assertEquals("Lf_KjX$*Z5ZK", string0);
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.shortToByteArray((byte) (-1), 1, byteArray0, 0, 1);
      assertArrayEquals(new byte[] {(byte) (-1), (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray((-3549L), 5, byteArray0, (-119), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -119
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      short[] shortArray0 = new short[3];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray((byte) (-55), (-724), shortArray0, (short)781, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 781
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray((-1815L), (-1797), shortArray0, 90, 93);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 90
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[1] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 1, (byte)11, (-321), (byte)11);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[12];
      byte byte0 = Conversion.binaryToByte(booleanArray0, (-355), (byte) (-1), (byte) (-1), (-355));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 2923, (byte) (-119), 2923, (byte) (-119));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, 13, (short)67, (short)67, 1002);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[4] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 0, 248L, (-1921), 1533);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 5
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      int[] intArray0 = new int[0];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, (byte) (-37), (-1318), (byte) (-37), (byte)1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -37
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.shortToByteArray((short)3221, (byte) (-41), byteArray0, 2003, (byte) (-41));
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      short[] shortArray0 = new short[1];
      long long0 = Conversion.shortArrayToLong(shortArray0, (-1159), 371L, 14, (-2468));
      assertEquals(371L, long0);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      short[] shortArray0 = new short[6];
      int int0 = Conversion.shortArrayToInt(shortArray0, 1082, (short) (-541), 1082, (short) (-541));
      assertEquals((-541), int0);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      int[] intArray0 = new int[3];
      int[] intArray1 = Conversion.longToIntArray(65535L, (-1318), intArray0, (-2216), (-1318));
      assertArrayEquals(new int[] {0, 0, 0}, intArray1);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      String string0 = Conversion.longToHex(536, 536, "", 0, 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.longToByteArray((byte) (-1), (-1), byteArray0, (byte) (-51), (byte) (-51));
      assertArrayEquals(new byte[] {}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      short[] shortArray0 = new short[0];
      short[] shortArray1 = Conversion.intToShortArray(0, 4303, shortArray0, (-1664), (-3769));
      assertArrayEquals(new short[] {}, shortArray1);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      int[] intArray0 = new int[1];
      long long0 = Conversion.intArrayToLong(intArray0, 0, (-176L), 0, (-4779));
      assertEquals((-176L), long0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.byteToBinary((byte) (-75), 1484, (boolean[]) null, 2436, (-1923));
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte) (-81), 101, booleanArray0, 101, (-5243));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      short short0 = Conversion.byteArrayToShort(byteArray0, (byte)0, (byte)0, (byte)0, (byte)0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      long long0 = Conversion.byteArrayToLong(byteArray0, (byte) (-45), (byte)43, (byte) (-92), 0);
      assertEquals(43L, long0);
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      int int0 = Conversion.byteArrayToInt(byteArray0, 672, 0, (byte)0, (byte)0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      short short0 = Conversion.binaryToShort(booleanArray0, (byte) (-19), (byte)0, (-1412), (-1));
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      long long0 = Conversion.binaryToLong(booleanArray0, (-1204), (-1204), (-1), (-1));
      assertEquals((-1204L), long0);
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray((UUID) null, byteArray0, (-3807), (-3334));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt((short[]) null, 64, 64, 64, 521);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(398, (-1438), (String) null, 1497, (-1438));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong((int[]) null, (-4779), 1719L, 1547, 1547);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte((String) null, (byte)122, (byte)122, (-2496), (byte)92);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid((byte[]) null, 600);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort((byte[]) null, 64, (short) (-348), 97, (-486));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt((byte[]) null, 0, 0, (-2945), (-641));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToLong((boolean[]) null, (byte)7, (-390L), (byte)7, (-728));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToInt((boolean[]) null, (byte)57, (-1441), 0, 1874);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (int) (short) (-884));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -881
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=3, srcPos=0
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null, 102);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToByte((boolean[]) null, 39, (byte) (-44), 39, 71);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byte[] byteArray1 = Conversion.longToByteArray((-360L), (-1096), byteArray0, 1323, (byte) (-37));
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[8] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (-950));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 101);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 101
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[14];
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, (int) (byte) (-29));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (byte)0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Need at least 16 bytes for UUID
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray((UUID) null, byteArray0, (byte)9, 2875);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBytes is greater than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)121, (-5243), booleanArray0, (byte)121, 10);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 121
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte) (-1), 89, booleanArray0, 1853, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte) (-113), 14, booleanArray0, (byte) (-113), (byte) (-113));
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)16, (-1), booleanArray0, (-2914), (-2283));
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)2453, (-1013), booleanArray0, (-9), 0);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)0, 1337, booleanArray0, 536, 1111);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('7');
      // Undeclared exception!
      try { 
        Conversion.intToBinary((byte)0, (-2229), booleanArray0, 451, 89);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 451
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((byte) (-81), 17, booleanArray0, 679, 101);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      boolean[] booleanArray1 = Conversion.intToBinary(32, 0, booleanArray0, 32, 0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.intToBinary((-411), 5, booleanArray0, (-411), (-1531));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.longToBinary(14, 14, booleanArray0, 385, 3223);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      boolean[] booleanArray1 = Conversion.longToBinary((-360L), (-1318), booleanArray0, (-573), 0);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte) (-27), (-917), "nBytes is greater than 16", (-1318), 65535);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)37, (byte)37, "", (-3334), 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte) (-1), (byte) (-1), "zPyF3Hm#KGcAn-7>VfP", 9, (-2914));
      assertEquals("zPyF3Hm#KGcAn-7>VfP", string0);
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)474, (short)474, "@AyO ", (short)474, (short)474);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)0, (short)0, "p'R(;", 0, (short)0);
      assertEquals("p'R(;", string0);
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      String string0 = Conversion.shortToHex((short) (-410), 1030, "", 57, (-930));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(1982, 1982, (String) null, 1982, 1244);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      String string0 = Conversion.intToHex(0, 398, ":],", 398, 0);
      assertEquals(":],", string0);
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      String string0 = Conversion.intToHex((-890), 16, "", (-890), (-890));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      String string0 = Conversion.longToHex(0L, (-762), "_H$!=yD)5;|", 2837, (-762));
      assertEquals("_H$!=yD)5;|", string0);
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex((-1L), 3081, "A;PWv>N+kRxt@>1aQ", (-607), 94);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short) (-1), (-857), byteArray0, (-2230), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2230
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.shortToByteArray((byte)103, 1172, byteArray0, 1172, 0);
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((byte)46, (-99), byteArray0, (-1), 1569);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.intToByteArray((-1867), (-2122), byteArray0, (byte)0, (-1667));
      assertEquals(2, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray((-1), 10, byteArray0, 10, 10);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, 111, (-7));
      assertEquals(0, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byte[] byteArray1 = Conversion.longToByteArray(0L, (-348), byteArray0, (byte)0, (byte)0);
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray(0L, 14, byteArray0, 2469, 37);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      short[] shortArray0 = Conversion.intToShortArray(57, (short)294, (short[]) null, (-1590), (-29617));
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      short[] shortArray0 = new short[2];
      short[] shortArray1 = Conversion.intToShortArray((-1), (-1592), shortArray0, (-1592), 0);
      assertEquals(2, shortArray1.length);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      short[] shortArray0 = new short[3];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray(528, 4, shortArray0, 58, 528);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      short[] shortArray0 = new short[5];
      short[] shortArray1 = Conversion.longToShortArray(0L, (-1558), shortArray0, (short)88, (-1));
      assertSame(shortArray0, shortArray1);
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray(15L, 89, shortArray0, 3257, 4);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      int[] intArray0 = new int[1];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray(1170, (-4429), intArray0, 1170, 82);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1170
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      int[] intArray0 = new int[2];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray((-2216), (-2216), intArray0, 12, 3550);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      int[] intArray0 = new int[0];
      int[] intArray1 = Conversion.longToIntArray(7, 1, intArray0, 3, (byte) (-113));
      assertSame(intArray0, intArray1);
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      booleanArray0[0] = true;
      byte byte0 = Conversion.binaryToByte(booleanArray0, 0, (byte)86, 0, 1);
      assertEquals((byte)87, byte0);
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 0, (byte) (-1), 0, 0);
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      short short0 = Conversion.binaryToShort(booleanArray0, (byte)79, (byte)92, (short)97, 0);
      assertEquals((short)92, short0);
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      short short0 = Conversion.binaryToShort(booleanArray0, (-4127), (short) (-426), 2095, (-4127));
      assertEquals((short) (-426), short0);
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 0, (byte)0, (-1172), 231);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 6, 6, 108, 108);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      int int0 = Conversion.binaryToInt(booleanArray0, (-1310), 1, 1, (-1310));
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      int int0 = Conversion.binaryToInt(booleanArray0, 0, 0, 0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 91, 91, 91, 91);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      long long0 = Conversion.binaryToLong(booleanArray0, 0, 0, 0, 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      long long0 = Conversion.binaryToLong(booleanArray0, (-1), 1912L, 1, (-17));
      assertEquals(1912L, long0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("?._\"l;St7UZ&dox", 2095, (byte) (-125), (short) (-426), (-1438));
      assertEquals((byte) (-125), byte0);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("L", 56, (byte) (-1), 1, 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      byte byte0 = Conversion.hexToByte((String) null, (-1174), (byte) (-8), (byte) (-8), 0);
      assertEquals((byte) (-8), byte0);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("", 99, (byte)79, 5, 89);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      short short0 = Conversion.hexToShort("X5;Q.Qn%-RBccF5u&a", 865, (short)83, 0, (-244));
      assertEquals((short)83, short0);
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      short short0 = Conversion.hexToShort("JmkqY]xBy6&Fe;?XH0P", 98, (short) (-417), (short) (-417), 0);
      assertEquals((short) (-417), short0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("JmkqY]xBy6&Fe;?XH0P", (-526), (short)93, (short) (-417), 914);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("", 63, 63, (-3992), 111);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      int int0 = Conversion.hexToInt("!KQy", 0, 0, (-3709), (-3709));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      int int0 = Conversion.hexToInt("h&", (-4637), 1, 0, 0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("", (-4779), 7, 1, 76);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      long long0 = Conversion.hexToLong("=uSP-~q", 0, (-18L), 0, (-1));
      assertEquals((-18L), long0);
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      long long0 = Conversion.hexToLong("", (-98), 3366L, 536, 0);
      assertEquals(3366L, long0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong((String) null, 64, 0L, 406, 56);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-584), (byte)37, (-116), (-116));
      assertEquals((short)37, short0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      short short0 = Conversion.byteArrayToShort(byteArray0, (byte) (-114), (short) (-912), 0, 0);
      assertEquals((short) (-912), short0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, 32, (short) (-609), (short) (-609), 57);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      short short0 = Conversion.byteArrayToShort(byteArray0, 0, (short)97, (short)97, 14);
      assertEquals((short)97, short0);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      byte[] byteArray0 = new byte[10];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, 2849, (byte)0, (-80), 2264);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, (byte) (-52), 99, (-2668), (byte)1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -52
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, 672, 0, 1662, (byte) (-81));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      int int0 = Conversion.byteArrayToInt(byteArray0, (-3334), (-3334), (byte)37, 0);
      assertEquals((-3334), int0);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      int int0 = Conversion.byteArrayToInt(byteArray0, 1, 102, (byte)118, (-39));
      assertEquals(102, int0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      byte[] byteArray0 = new byte[4];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, (byte)78, 1L, (byte)78, (byte)78);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      long long0 = Conversion.byteArrayToLong(byteArray0, (byte)0, 0L, (byte)0, (byte)0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-1651), (-1651), 0, (-1651));
      assertEquals((-1651L), long0);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (-165));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -165
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      short[] shortArray0 = new short[6];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (short) (-1), (short)1169, (-1033), (short)1278);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      short[] shortArray0 = new short[0];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short) (-116), 102, 242, 0);
      assertEquals(102, int0);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, (short)181, 1638L, (-1), 5);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 181
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      short[] shortArray0 = new short[0];
      long long0 = Conversion.shortArrayToLong(shortArray0, 1853, 0L, (-2771), (byte) (-1));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      short[] shortArray0 = new short[1];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, 0, (-2387L), 1, 336);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      int[] intArray0 = new int[4];
      long long0 = Conversion.intArrayToLong(intArray0, 269, 0L, 53, (-1571));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      int[] intArray0 = new int[0];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, 788, 788, 51, 788);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      int[] intArray0 = new int[1];
      long long0 = Conversion.intArrayToLong(intArray0, 7, 0, 0, (short)0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigitMsb0((-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(9);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(1);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigit((-2122));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: -2122
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex((-2304L), (-1558), "M0j`gn", 64, 64);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 64
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[4] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[6] = true;
      booleanArray0[7] = true;
      booleanArray0[8] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[6] = true;
      booleanArray0[7] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[4] = true;
      booleanArray0[5] = true;
      booleanArray0[7] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=3, srcPos=0
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('-');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '-' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('@');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '@' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('9');
      assertTrue(Arrays.equals(new boolean[] {true, false, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('7');
      assertTrue(Arrays.equals(new boolean[] {false, true, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('\u0012');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '\u0012' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('c');
      assertTrue(Arrays.equals(new boolean[] {false, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('`');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '`' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('_');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '_' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('W');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'W' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('Q');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Q' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('R');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'R' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('7');
      boolean[] booleanArray1 = Conversion.longToBinary((-554), (-2229), booleanArray0, (-3960), (-3960));
      assertTrue(Arrays.equals(new boolean[] {true, true, true, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('a');
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('k');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'k' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('7');
      assertEquals(14, int0);
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToInt('K');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'K' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('8');
      assertEquals(8, int0);
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      Conversion conversion0 = new Conversion();
  }
}
