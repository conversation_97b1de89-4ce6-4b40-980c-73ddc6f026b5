/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:17:13 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.Conversion;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.util.MockUUID;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Conversion_ESTest extends Conversion_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)1, (short)1, booleanArray0, (short)1, (short)1);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex((-1202), 0, "P6)/FQ", (-1202), 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -1202
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex((-1202), (-3169), "P6)/,FQ", 3792, 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 3792
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      byteArray0[0] = (byte) (-61);
      short short0 = Conversion.byteArrayToShort(byteArray0, (byte)0, (short) (-2031), (byte) (-61), 1);
      assertEquals((short) (-487), short0);
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, 5, (short)583, (short)583, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      byte[] byteArray0 = new byte[15];
      byteArray0[0] = (byte)16;
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, 0, 4, (-372), (byte)16);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 15
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      int int0 = Conversion.byteArrayToInt(byteArray0, 1198, (-1557), 0, (byte) (-70));
      assertEquals((-1557), int0);
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      String string0 = Conversion.shortToHex((short) (-714), (-8), "=Dj", (-8), (byte) (-2));
      assertEquals("=Dj", string0);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.shortToBinary((short) (-549), (short) (-549), (boolean[]) null, (short) (-549), (short) (-549));
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      short[] shortArray0 = new short[4];
      long long0 = Conversion.shortArrayToLong(shortArray0, 93, (-860L), 0, (-3709));
      assertEquals((-860L), long0);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      short[] shortArray0 = new short[3];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short) (-1), 39, (short) (-1), (short) (-1));
      assertEquals(39, int0);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      short[] shortArray0 = new short[1];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short) (-1), (short) (-1), (short) (-1), (short) (-1));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      int[] intArray0 = Conversion.longToIntArray(1892L, 0, (int[]) null, 0, 0);
      assertNull(intArray0);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      String string0 = Conversion.longToHex(1695L, 0, "", 0, (-2261));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      short[] shortArray0 = new short[2];
      short[] shortArray1 = Conversion.intToShortArray((-3868), (-1), shortArray0, 552, (-435));
      assertEquals(2, shortArray1.length);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      short[] shortArray0 = new short[0];
      short[] shortArray1 = Conversion.intToShortArray(10, 0, shortArray0, 10, (-1871));
      assertSame(shortArray0, shortArray1);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      String string0 = Conversion.intToHex(0, (-1), (String) null, 4, 0);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.intToByteArray(1757, 0, byteArray0, 1757, 0);
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      int[] intArray0 = new int[7];
      long long0 = Conversion.intArrayToLong(intArray0, 0, (-223), 0, (-216));
      assertEquals((-223L), long0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      short short0 = Conversion.hexToShort((String) null, 6849, (byte)16, 16, (-1));
      assertEquals((short)16, short0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      long long0 = Conversion.hexToLong("^i.4^Cj57pBEX*b(XZ", 0, 514L, 6, 0);
      assertEquals(514L, long0);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      int int0 = Conversion.hexToInt("Cw!rCFm&-_&f", (-114), (byte)71, 2, (-114));
      assertEquals(71, int0);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", (-3133), (byte)64, (short)33, (-753));
      assertEquals((byte)64, byte0);
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.longToBinary(1L, (-3242), booleanArray0, 7, (-3242));
      boolean[] booleanArray2 = Conversion.byteToBinary((byte) (-1), 0, booleanArray1, 1, (-1));
      assertSame(booleanArray1, booleanArray2);
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-2521), 255L, (byte) (-7), (-1210));
      assertEquals(255L, long0);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      long long0 = Conversion.byteArrayToLong(byteArray0, (byte)6, (byte) (-121), (byte)1, (-1));
      assertEquals((-121L), long0);
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      byte[] byteArray0 = new byte[4];
      int int0 = Conversion.byteArrayToInt(byteArray0, 839, 0, (-2070), 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      short short0 = Conversion.binaryToShort(booleanArray0, (byte) (-121), (byte) (-121), 0, (-3252));
      assertEquals((short) (-121), short0);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      long long0 = Conversion.binaryToLong(booleanArray0, (-1), 0L, (-634), 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      long long0 = Conversion.binaryToLong(booleanArray0, 1822, 255L, 928, (-3868));
      assertEquals(255L, long0);
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 0, (byte)57, (-3010), (-3524));
      assertEquals((byte)57, byte0);
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray((UUID) null, byteArray0, 81, (-2945));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)1, 0, (String) null, (-40), (short)1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong((short[]) null, (-1816), 1L, 14, (-2786));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt((short[]) null, 46, 46, (byte)50, 1563);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(853L, 1, (String) null, 1, 1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray(0L, 1, byteArray0, 1264, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1264
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong((int[]) null, (byte)68, 0, 16, 0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)2, 2, (String) null, 0, (-1432));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid((byte[]) null, 2310);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort((byte[]) null, 0, (short)101, 0, 56);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, 319, (short)9, (-3782), 96);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 319
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong((byte[]) null, (-33), (byte)49, 0, (-1266));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt((byte[]) null, (byte) (-42), (-3624), 0, 2331);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToLong((boolean[]) null, 86, 1057, (-1568), 86);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null, (int) (byte)102);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null, (-106));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit((boolean[]) null, 0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 63);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      byte[] byteArray0 = Conversion.longToByteArray((-4164), (short) (-227), (byte[]) null, (-731), (-1));
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (int) (short)0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=4, srcPos=1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, 0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Need at least 16 bytes for UUID
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (-1382));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1382
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, 2366, 1534);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBytes is greater than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte)100, (byte)100, booleanArray0, (byte)100, (-1135));
      assertEquals(9, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte) (-2), (-2460), (boolean[]) null, (-324), 13);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte) (-94), 1507, booleanArray0, (-1135), 0);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte) (-118), 12, booleanArray0, 629, 1629);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short) (-1), 0, booleanArray0, (-1), 2);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short) (-2647), 774, booleanArray0, 1334, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)55, 2066, booleanArray0, (-528), 0);
      assertEquals(8, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((-3466), (-162), booleanArray0, 1, 11);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(11, 11, booleanArray0, 11, 11);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 11
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.intToBinary((-1748), (-2945), booleanArray0, (-71), (-1));
      assertEquals(0, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      boolean[] booleanArray1 = Conversion.intToBinary(0, 0, booleanArray0, 0, 0);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(15, 98, booleanArray0, 15, 56);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.longToBinary((-5L), 8, booleanArray0, (byte) (-12), 14);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -12
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.longToBinary(2748L, (-2521), booleanArray0, (-2521), 483);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2521
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[24];
      // Undeclared exception!
      try { 
        Conversion.longToBinary(3091L, 5096, booleanArray0, (-2521), (-38));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      boolean[] booleanArray1 = Conversion.longToBinary(3091L, 5096, booleanArray0, (-2521), 0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)32, (-663), "^z[FK}]-YbZUcZ?", (-731), 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -731
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte) (-2), 75, "+J9.Y", 643, 643);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)64, (byte)64, "", (-394), 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte) (-61), (byte) (-61), "T1_lp0X`p", (byte) (-61), (-3169));
      assertEquals("T1_lp0X`p", string0);
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short) (-1370), (-669), "U^Jfc<XUmpW82}u'", (short) (-1370), 12);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -1370
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)33, (short)33, "", 0, (short)33);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)55, (-1), "", 1961, 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(1262, 2648, "", 27, 1262);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      String string0 = Conversion.intToHex(0, (-3524), "", (-3524), 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      String string0 = Conversion.intToHex((-634), (-634), "5Y7C|", 27, (-227));
      assertEquals("5Y7C|", string0);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex((short) (-1), (short) (-1), "", 2169, 10);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 2169
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex((short) (-1), 39, "", 2169, 10);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      String string0 = Conversion.longToHex(0, 0, (String) null, 0, 0);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      String string0 = Conversion.longToHex(11, 11, ":@WBK)*CnI+eg.", (-1), (-1));
      assertEquals(":@WBK)*CnI+eg.", string0);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((byte)16, (byte) (-96), byteArray0, (byte)16, 1529);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.shortToByteArray((short) (-1871), 0, byteArray0, 1798, 0);
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      byte[] byteArray0 = Conversion.shortToByteArray((short)4, (-2032), (byte[]) null, (-319), (-2));
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToByteArray((short) (-859), (-1273), (byte[]) null, 96, 96);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.intToByteArray(0, 0, byteArray0, 0, (byte) (-1));
      assertEquals(2, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      byte[] byteArray0 = Conversion.intToByteArray(0, (short)0, (byte[]) null, 1488, 0);
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray(0, 54, byteArray0, 0, 1757);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToByteArray(0L, (-3582), (byte[]) null, (-3582), 1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("thp");
      byte[] byteArray0 = Conversion.uuidToByteArray(uUID0, (byte[]) null, (byte) (-2), (-2509));
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      byte[] byteArray1 = Conversion.longToByteArray(0, 0, byteArray0, 0, 0);
      assertEquals(7, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray((-1748), 3414, byteArray0, (byte) (-15), 166);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      short[] shortArray0 = new short[6];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray(1, 1, shortArray0, (-2710), 1801);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      short[] shortArray0 = Conversion.intToShortArray((-1748), (-1748), (short[]) null, (-1748), (-1748));
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      short[] shortArray0 = new short[0];
      short[] shortArray1 = Conversion.longToShortArray((-1327L), 64, shortArray0, (-1), (-1));
      assertSame(shortArray1, shortArray0);
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      short[] shortArray0 = new short[0];
      short[] shortArray1 = Conversion.longToShortArray(15L, 101, shortArray0, 0, 0);
      assertEquals(0, shortArray1.length);
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray(514L, (byte)4, shortArray0, 7692, 13);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      int[] intArray0 = new int[3];
      int[] intArray1 = Conversion.longToIntArray(51, 0, intArray0, 0, (-1914));
      assertSame(intArray1, intArray0);
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      int[] intArray0 = new int[8];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray((-3170L), 1, intArray0, (-1843), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1843
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      int[] intArray0 = new int[1];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray((-2044L), 3703, intArray0, 3703, 3703);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 1270, (byte)1, (-2206), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1270
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 14, (byte)0, (-881), (-2461));
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 0, (byte) (-67), 0, 0);
      assertEquals((byte) (-67), byte0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, (byte)0, (byte) (-1), 1, 56);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      short short0 = Conversion.binaryToShort(booleanArray0, (-1889), (short)3322, (-1889), (byte) (-2));
      assertEquals((short)3322, short0);
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, 1871, (byte)0, 15, 15);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, (-1), (short)524, (-1848), 99);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      int int0 = Conversion.binaryToInt(booleanArray0, (-1129), (-1129), (-1843), (-1129));
      assertEquals((-1129), int0);
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      int int0 = Conversion.binaryToInt(booleanArray0, 0, 0, (-1843), 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      int int0 = Conversion.binaryToInt(booleanArray0, 4, 4, (-1843), 0);
      assertEquals(4, int0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 3055, 3055, 3055, 3055);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 14, 907L, 14, 14);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 14
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      long long0 = Conversion.binaryToLong(booleanArray0, 27, (-11L), 27, 0);
      assertEquals((-11L), long0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, (-1417), (-3762L), 1998, 1998);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte((String) null, (-1488), (byte)56, (-822), 1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("", (short)167, (byte) (-9), 485, (short)167);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", 0, (byte)0, (byte)0, 0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("Sb", 1, (byte) (-2), 1, (-1488));
      assertEquals((byte) (-2), byte0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      short short0 = Conversion.hexToShort("", (-1651), (short) (-714), 2117, (-3868));
      assertEquals((short) (-714), short0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("", (-748), (short)1348, (-1210), (short)1348);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("KHd%2%om", (-748), (short)4, (short)4, 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      int int0 = Conversion.hexToInt("u=O4O7&/~t", 505, (-1), (-1557), (-1557));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("K#WgUhg;{RHcal", 472, (byte)4, 472, 2636);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong("nBools-1+dstPos is greater or equal to than 32", 97, (short)583, (-1877), 83);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong("nBools-1+dstPos is greater or equal to than 32", (-2509), (-1L), 83, (byte) (-2));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      long long0 = Conversion.hexToLong("", (short)0, (short)0, (-79), (short)0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      long long0 = Conversion.hexToLong("nBools-1+dstPos is greater or equal to than 32", (-2509), (byte) (-2), (byte) (-2), (byte) (-2));
      assertEquals((-2L), long0);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      short short0 = Conversion.byteArrayToShort(byteArray0, (byte)0, (short)1, (byte) (-115), 0);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, (-30707), (short)64, 1, (byte)64);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-2509), (short)101, 98, (-255));
      assertEquals((short)101, short0);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, 1187, 75, 1187, 83);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      int int0 = Conversion.byteArrayToInt(byteArray0, (byte)14, 2217, (-1635), (byte)0);
      assertEquals(2217, int0);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      int int0 = Conversion.byteArrayToInt(byteArray0, (short) (-714), (byte) (-2), 1, (-8));
      assertEquals((-2), int0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      long long0 = Conversion.byteArrayToLong(byteArray0, 0, 0, 0, (-1));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-1), 0, (-1), 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, (-1), (-1731L), 1267, (-8));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, (-1621), 153L, 1, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1621
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      short[] shortArray0 = new short[2];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (short) (-2), (short) (-2), (-2047), 96);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (byte) (-2), 3021, 3021, 39);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      short[] shortArray0 = new short[1];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short) (-1), 0, (short) (-1), (short) (-1));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      short[] shortArray0 = new short[2];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, (-1739), (short)64, (-1739), (short)64);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1739
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      short[] shortArray0 = new short[4];
      long long0 = Conversion.shortArrayToLong(shortArray0, 9, 1114L, (-3709), (-1));
      assertEquals(1114L, long0);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, 1909, (byte)53, (byte)0, 1909);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      short[] shortArray0 = new short[2];
      long long0 = Conversion.shortArrayToLong(shortArray0, (short)0, (short)0, (short)167, (short)0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      int[] intArray0 = new int[5];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, 0, (-1605), (-1605), 1244);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      int[] intArray0 = new int[7];
      long long0 = Conversion.intArrayToLong(intArray0, (-302), 15L, 4, 0);
      assertEquals(15L, long0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigitMsb0((byte)100);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: 100
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigit((byte) (-2));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: -2
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[8] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[7] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[5] = true;
      booleanArray0[8] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[3] = true;
      booleanArray0[5] = true;
      booleanArray0[6] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[3] = true;
      booleanArray0[5] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      booleanArray0[6] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      booleanArray0[5] = true;
      booleanArray0[6] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      booleanArray0[5] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 3599);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 54);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 54
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('e');
      assertTrue(Arrays.equals(new boolean[] {true, true, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('a');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('`');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '`' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('U');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'U' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('B');
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('<');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '<' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('/');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '/' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary(' ');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ' ' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('9');
      assertTrue(Arrays.equals(new boolean[] {true, false, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('3');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('0');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('o');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'o' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('c');
      assertEquals(3, int0);
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('_');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '_' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('D');
      assertEquals(11, int0);
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('A');
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('6');
      assertEquals(6, int0);
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('U');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'U' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToInt('j');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'j' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('B');
      assertEquals(11, int0);
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=2, srcPos=0
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      Conversion conversion0 = new Conversion();
  }
}
