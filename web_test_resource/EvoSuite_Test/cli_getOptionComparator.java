/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:45:36 GMT 2025
 */

package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.util.Comparator;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.OptionGroup;
import org.apache.commons.cli.Options;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockFileOutputStream;
import org.evosuite.runtime.mock.java.io.MockPrintWriter;
import org.evosuite.runtime.testdata.EvoSuiteFile;
import org.evosuite.runtime.testdata.FileSystemHandling;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class HelpFormatter_ESTest extends HelpFormatter_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals(74, helpFormatter0.defaultWidth);
      
      helpFormatter0.setArgName(" | ");
      int int0 = (-895);
      helpFormatter0.setLeftPadding((-895));
      helpFormatter0.getNewLine();
      FileSystemHandling.appendLineToFile((EvoSuiteFile) null, "arg");
      FileSystemHandling.shouldThrowIOException((EvoSuiteFile) null);
      helpFormatter0.getLeftPadding();
      helpFormatter0.getLongOptPrefix();
      String string0 = "_,_CFuJZ";
      String string1 = "na";
      HelpFormatter helpFormatter1 = new HelpFormatter();
      Comparator<Option> comparator0 = helpFormatter1.optionComparator;
      int int1 = 994;
      String string2 = "";
      String string3 = null;
      Options options0 = new Options();
      Options options1 = options0.addRequiredOption("GM_", ";2N4u^><(", false, (String) null);
      // Undeclared exception!
      try { 
        options1.addOption(" | ", "@_?$A>^WVf*+");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option ' | ' contains an illegal character : ' '
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getOptPrefix();
      int int0 = 651;
      Options options0 = new Options();
      // Undeclared exception!
      try { 
        options0.addRequiredOption("<", "--", false, "<");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal option name '<'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      StringWriter stringWriter0 = new StringWriter(3);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(stringWriter0);
      String string0 = ">";
      helpFormatter0.setLongOptSeparator(">");
      String string1 = "";
      helpFormatter0.printWrapped((PrintWriter) mockPrintWriter0, 1620, "");
      int int0 = 2502;
      String string2 = "l.X(b;RkO^#d";
      Options options0 = new Options();
      String string3 = null;
      OptionGroup optionGroup0 = new OptionGroup();
      Option option0 = null;
      try {
        option0 = new Option("MLJN:p#", "--");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'MLJN:p#' contains an illegal character : ':'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "[";
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter("[");
      String string1 = "#XXcGB&]!IOh\\I";
      Object[] objectArray0 = new Object[5];
      objectArray0[0] = (Object) "#XXcGB&]!IOhI";
      objectArray0[1] = (Object) "[";
      mockPrintWriter0.append('E');
      objectArray0[2] = (Object) helpFormatter0;
      objectArray0[3] = (Object) helpFormatter0;
      objectArray0[4] = (Object) mockPrintWriter0;
      PrintWriter printWriter0 = mockPrintWriter0.format("#XXcGB&]!IOhI", objectArray0);
      MockPrintWriter mockPrintWriter1 = new MockPrintWriter(printWriter0);
      PrintWriter printWriter1 = mockPrintWriter1.append((CharSequence) "-");
      helpFormatter0.setLeftPadding(191);
      int int0 = (-889);
      int int1 = 1604;
      // Undeclared exception!
      try { 
        helpFormatter0.printWrapped(printWriter1, (-889), 1604, "[");
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      int int0 = 0;
      StringWriter stringWriter0 = new StringWriter(0);
      char char0 = '%';
      StringWriter stringWriter1 = stringWriter0.append('%');
      StringBuffer stringBuffer0 = stringWriter1.getBuffer();
      int int1 = 0;
      String string0 = ",aueiTR'3^T\"p]${B";
      // Undeclared exception!
      helpFormatter0.renderWrappedText(stringBuffer0, int0, int1, string0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "--";
      helpFormatter0.rtrim("--");
      MockFile mockFile0 = new MockFile("}k<#0Us1)Q+[kMgX3");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFile0);
      mockFile0.setReadable(false, false);
      Options options0 = null;
      mockFile0.toURI();
      int int0 = 0;
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((PrintWriter) mockPrintWriter0, (-599), (String) null, "}k<#0Us1)Q+[kMgX3", (Options) null, (-2104), 0, "WkH`--?$]<X9*Fcx", true);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      Comparator<Option> comparator0 = helpFormatter0.getOptionComparator();
      helpFormatter0.setOptionComparator(comparator0);
      int int0 = 0;
      helpFormatter0.setLeftPadding(0);
      String string0 = null;
      helpFormatter0.setNewLine((String) null);
      int int1 = 1522;
      StringWriter stringWriter0 = new StringWriter(1522);
      // Undeclared exception!
      try { 
        stringWriter0.append((CharSequence) "--", 74, 74);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      int int0 = 0;
      StringWriter stringWriter0 = new StringWriter(0);
      StringWriter stringWriter1 = stringWriter0.append('a');
      StringBuffer stringBuffer0 = stringWriter1.getBuffer();
      int int1 = 0;
      String string0 = ",aueiTR'3^T\"p]${B";
      // Undeclared exception!
      helpFormatter0.renderWrappedText(stringBuffer0, int0, int1, string0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "\\U>~\\sz";
      helpFormatter0.setLongOptSeparator("U>~sz");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter("f-!");
      PrintWriter printWriter0 = mockPrintWriter0.append('[');
      int int0 = 1;
      String string1 = "+sU0}9Kj6%'\\";
      // Undeclared exception!
      helpFormatter0.printUsage(printWriter0, int0, string1);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setOptPrefix("org.apache.commons.cli.ParseException");
      int int0 = 32;
      String string0 = "6x&mIENe";
      Options options0 = new Options();
      Option option0 = null;
      try {
        option0 = new Option("5~QLi1:qaMSRAQcI8mD", "usage: ");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '5~QLi1:qaMSRAQcI8mD' contains an illegal character : '~'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setLeftPadding(1);
      assertEquals(3, helpFormatter0.defaultDescPad);
      assertEquals(1, helpFormatter0.defaultLeftPad);
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      assertEquals("arg", helpFormatter0.getArgName());
      assertEquals("-", helpFormatter0.getOptPrefix());
      assertEquals("usage: ", helpFormatter0.getSyntaxPrefix());
      assertEquals(" ", helpFormatter0.getLongOptSeparator());
      assertEquals(74, helpFormatter0.defaultWidth);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "";
      Comparator<Option> comparator0 = helpFormatter0.getOptionComparator();
      helpFormatter0.optionComparator = comparator0;
      helpFormatter0.setLeftPadding(0);
      helpFormatter0.defaultNewLine = "";
      helpFormatter0.getWidth();
      Options options0 = new Options();
      OptionGroup optionGroup0 = new OptionGroup();
      Options options1 = options0.addOptionGroup(optionGroup0);
      Option option0 = new Option("", "--", false, "");
      Options options2 = options1.addOption(option0);
      // Undeclared exception!
      try { 
        options2.addOption("-", false, "7qq<h<{");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal option name '-'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = "";
      helpFormatter0.setArgName("");
      helpFormatter0.setSyntaxPrefix("org.apache.commons.cli.OptionGroup");
      int int0 = 1;
      Options options0 = null;
      helpFormatter0.setArgName("");
      int int1 = (-2);
      helpFormatter0.setWidth((-2));
      String string1 = "o;GK";
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("LG11wB')r2aa.", "gW?7)(n", (Options) null, "o;GK", false);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      StringWriter stringWriter0 = new StringWriter(1);
      StringWriter stringWriter1 = stringWriter0.append('%');
      StringBuffer stringBuffer0 = stringWriter1.getBuffer();
      helpFormatter0.renderWrappedText(stringBuffer0, 1, 0, ",aueiTR'3^T\"p]${B");
      assertEquals(36, stringBuffer0.length());
      assertEquals("%,\n\na\nu\ne\ni\nT\nR\n'\n3\n^\nT\n\"\np\n]\n$\n{\nB", stringWriter0.toString());
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getArgName();
      helpFormatter0.getNewLine();
      int int0 = 1657;
      helpFormatter0.findWrapPos("\n", 1657, 854);
      boolean boolean0 = false;
      MockFileOutputStream mockFileOutputStream0 = null;
      try {
        mockFileOutputStream0 = new MockFileOutputStream((File) null, false);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.io.File", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      int int0 = (-2398);
      // Undeclared exception!
      try { 
        helpFormatter0.printWrapped((PrintWriter) null, (-2398), "");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.setLongOptPrefix("o%Hw");
      helpFormatter0.setSyntaxPrefix(" ] [ long ");
      MockPrintWriter mockPrintWriter0 = null;
      try {
        mockPrintWriter0 = new MockPrintWriter("pR3r1&/hcx", "arg");
        fail("Expecting exception: UnsupportedEncodingException");
      
      } catch(Throwable e) {
         //
         // arg
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockPrintWriter", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      assertEquals("--", helpFormatter0.getLongOptPrefix());
      
      helpFormatter0.setLongOptPrefix("");
      helpFormatter0.getSyntaxPrefix();
      assertEquals("", helpFormatter0.getLongOptPrefix());
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      PrintWriter printWriter0 = null;
      int int0 = (-1629);
      String string0 = "";
      String string1 = "-";
      Options options0 = null;
      String string2 = "";
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp(0, (String) null, "", (Options) null, "", true);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = null;
      helpFormatter0.setLongOptPrefix((String) null);
      Options options0 = null;
      Comparator<Option> comparator0 = helpFormatter0.getOptionComparator();
      helpFormatter0.optionComparator = comparator0;
      helpFormatter0.setLongOptSeparator("Jy");
      helpFormatter0.defaultArgName = "]";
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp((String) null, (String) null, (Options) null, "org.apache.commons.cli.HelpFormatter", true);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      helpFormatter0.getWidth();
      String string0 = "";
      Options options0 = new Options();
      boolean boolean0 = false;
      options0.hasLongOption("");
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp(74, "", "NO_ARGS_ALLOWED", options0, "6Hk", false);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cmdLineSyntax not provided
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      // Undeclared exception!
      try { 
        helpFormatter0.printHelp("xy*sfqJ", "xy*sfqJ", (Options) null, "");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.HelpFormatter", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      String string0 = null;
      helpFormatter0.rtrim((String) null);
      int int0 = (-273);
      String string1 = "";
      Options options0 = new Options();
      Options options1 = options0.addOption((String) null, "org.apache.commons.cli.HelpFormatter$1", false, "7d sEg:0+~4Q$#5gb");
      OptionGroup optionGroup0 = new OptionGroup();
      helpFormatter0.createPadding(4316);
      Options options2 = options1.addOptionGroup(optionGroup0);
      String string2 = "[";
      options0.getOption("<t\"q#2`kV&H[1MvK");
      // Undeclared exception!
      try { 
        options2.addRequiredOption("[", "--", false, (String) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal option name '['
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      HelpFormatter helpFormatter0 = new HelpFormatter();
      StringWriter stringWriter0 = new StringWriter();
      int int0 = (-1134);
      // Undeclared exception!
      try { 
        stringWriter0.append((CharSequence) "arg", (-1134), 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }
}
