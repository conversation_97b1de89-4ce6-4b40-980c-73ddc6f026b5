/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:20:37 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.Conversion;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.util.MockUUID;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Conversion_ESTest extends Conversion_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)52, (-1211), "", (byte)79, 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 79
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)1148, 0, (String) null, 0, 5);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex((short) (-1411), (short)1, "", (-1612), (short)1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -1612
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      int[] intArray0 = new int[9];
      int[] intArray1 = Conversion.longToIntArray(756L, (-2519), intArray0, 0, 1);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0, 0, 0}, intArray1);
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, (short)0, 495, (short)1, 64);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byteArray0[0] = (byte)99;
      int int0 = Conversion.byteArrayToInt(byteArray0, (short)0, (byte) (-68), (-3517), (short)1);
      assertEquals((-1252), int0);
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      short[] shortArray0 = new short[0];
      int int0 = Conversion.shortArrayToInt(shortArray0, 8, 0, (-2093), (-2093));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, 596, (byte)9, (-1), 2529);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.longToBinary(15L, 0, booleanArray0, (short)0, (-1));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      String string0 = Conversion.intToHex(0, (-487), (String) null, 0, 0);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.intToByteArray(55, 55, byteArray0, 55, (-1081));
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.intToBinary((short)1, (short) (-1411), booleanArray0, (short) (-327), (short) (-327));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      int[] intArray0 = new int[2];
      long long0 = Conversion.intArrayToLong(intArray0, 7, (-1L), (-2046), (-4805));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('d');
      assertEquals(13, int0);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      short short0 = Conversion.byteArrayToShort(byteArray0, (byte) (-1), (short)0, (byte) (-1), (byte) (-1));
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      short short0 = Conversion.byteArrayToShort(byteArray0, 6, (short)255, (-1655), (-2388));
      assertEquals((short)255, short0);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      int int0 = Conversion.byteArrayToInt(byteArray0, (-3517), 75, (byte) (-89), (-255));
      assertEquals(75, int0);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      short short0 = Conversion.binaryToShort(booleanArray0, (-2251), (short)0, 2, 0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      long long0 = Conversion.binaryToLong(booleanArray0, (byte) (-87), 0, (short)1, (-398));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      int int0 = Conversion.binaryToInt(booleanArray0, 0, (-2859), (-1254), 0);
      assertEquals((-2859), int0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 0, (byte)0, (byte)0, (-1889));
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      byte byte0 = Conversion.binaryToByte(booleanArray0, (byte)107, (byte)11, (byte)31, (-374));
      assertEquals((byte)11, byte0);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong((short[]) null, 0, 0L, (byte)14, 1006);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt((short[]) null, (short)1, 403, 394, (-811));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong((int[]) null, (byte)52, (byte)52, 1, 56);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong((String) null, 9, 33L, (-1947), 97);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte) (-1), (-700), "jVZ7ZSAdQtK0P+2-.U", (-700), '9');
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -700
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort((byte[]) null, (-3431), (short)2617, (-3968), 68);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong((byte[]) null, 2320, 2320, 71, 5);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt((byte[]) null, 2280, 2280, 2280, 2280);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, (byte)1, 893, (-1745), 99);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 7
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null, 95);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null, 197);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 3);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 74);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=7, srcPos=74
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (byte)42);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Need at least 16 bytes for UUID
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      UUID uUID0 = MockUUID.fromString("[Z-&969x");
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, 102, 100);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBytes is greater than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte) (-102), (-613), booleanArray0, 1620, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1620
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte) (-99), 0, booleanArray0, (-3455), (byte) (-99));
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte) (-4), (-3455), booleanArray0, (-391), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -391
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte) (-4), 0, booleanArray0, 98, 0);
      assertEquals(6, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)2, (short)57, (boolean[]) null, (-727), 174);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.shortToBinary((short)14, 50, (boolean[]) null, 16, (byte) (-73));
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)1123, (short)0, booleanArray0, (short)0, (short)0);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)101, (-1), booleanArray0, (-1), 1041);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      boolean[] booleanArray1 = Conversion.intToBinary(0, 0, booleanArray0, 0, 4);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      boolean[] booleanArray1 = Conversion.intToBinary(0, 0, booleanArray0, 0, 0);
      assertEquals(6, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(13, 100, booleanArray0, (-497), 100);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.longToBinary((-1202), (byte) (-123), booleanArray0, (-1202), (byte)107);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1202
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      boolean[] booleanArray1 = Conversion.longToBinary((-1202), (-1202), booleanArray0, 1, 1);
      assertTrue(Arrays.equals(new boolean[] {false, true, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      boolean[] booleanArray1 = Conversion.longToBinary(0L, 0, booleanArray0, 0, 0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToBinary(15L, (short)1773, (boolean[]) null, 102, 102);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)22, (byte)1, "6xaZj<[qEXw nDR0Ax", 1, 1);
      assertEquals("6baZj<[qEXw nDR0Ax", string0);
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)82, (byte)82, "tqa;?$B\u0007Wu]s%>>L", 101, (-1346));
      assertEquals("tqa;?$B\u0007Wu]s%>>L", string0);
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)2, (byte)2, "", 52, 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)8, (byte)8, "", 3114, 69);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)0, 0, (String) null, 0, (-1));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)2878, (-213), "sEL[G1vMDP", (-1), (short)2878);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(2815, 0, (String) null, (-592), 101);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      String string0 = Conversion.intToHex(4, (-4), "I}>=1goz\"0bI$z", (-2251), 0);
      assertEquals("I}>=1goz\"0bI$z", string0);
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      String string0 = Conversion.intToHex(0, (-982), "nBytes is greater than 16", (-1150), (-1150));
      assertEquals("nBytes is greater than 16", string0);
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      String string0 = Conversion.longToHex(4, 0, "", 0, (-1612));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(742L, 96, "p6~", (-630), 97);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      String string0 = Conversion.longToHex(0L, 0, "}<cLq", (-2476), 0);
      assertEquals("}<cLq", string0);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(4294967295L, (-3894), "", 71, 93);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 71
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short) (-625), (-245), (byte[]) null, 0, 2);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short) (-1), 5, byteArray0, (byte) (-1), 203);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byte[] byteArray1 = Conversion.shortToByteArray((short) (-944), (-224), byteArray0, 9, 0);
      assertEquals(5, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byte[] byteArray1 = Conversion.shortToByteArray((byte)1, (-1150), byteArray0, 988, (-2567));
      assertEquals(5, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.intToByteArray((byte)0, (-1), byteArray0, (-2889), (-1190));
      assertArrayEquals(new byte[] {(byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      byte[] byteArray1 = Conversion.intToByteArray(116, 116, byteArray0, 688, 0);
      assertEquals(6, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray((byte)99, 15, byteArray0, (byte) (-1), 1265);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byte[] byteArray1 = Conversion.longToByteArray(0, 0, byteArray0, (byte) (-86), (byte) (-86));
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byte[] byteArray1 = Conversion.longToByteArray(2844, 0, byteArray0, 2844, 0);
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray((-281L), 400, byteArray0, 400, 6429);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray((byte)84, (short) (-2004), shortArray0, (byte)84, 97);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 84
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      short[] shortArray0 = new short[1];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray(55, (-2397), shortArray0, (-2397), 1541);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      short[] shortArray0 = new short[1];
      short[] shortArray1 = Conversion.intToShortArray(0, 54, shortArray0, (short)3242, 0);
      assertSame(shortArray1, shortArray0);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      short[] shortArray0 = Conversion.intToShortArray((short)0, (-81), (short[]) null, (-2838), (-2838));
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray((-2023L), (short)1, shortArray0, 2062, (short)1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2062
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      short[] shortArray0 = Conversion.longToShortArray(617L, (byte) (-123), (short[]) null, 102, (short) (-123));
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      short[] shortArray0 = new short[1];
      short[] shortArray1 = Conversion.longToShortArray((-1L), 83, shortArray0, (-13703), 0);
      assertSame(shortArray1, shortArray0);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      short[] shortArray0 = new short[5];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray(1L, 297, shortArray0, 2085, (short)4);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      int[] intArray0 = new int[1];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray((byte)2, (-1211), intArray0, 1, 8);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      int[] intArray0 = new int[5];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray(0L, 0, intArray0, 0, 16);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      int[] intArray0 = Conversion.longToIntArray(0, (short)0, (int[]) null, 32, (byte)0);
      assertNull(intArray0);
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      int[] intArray0 = new int[3];
      int[] intArray1 = Conversion.longToIntArray((-199L), (-1130), intArray0, (-1130), (-1130));
      assertSame(intArray1, intArray0);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, (byte)14, (byte)57, 2459, 98);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 0, (byte) (-11), 9, 0);
      assertEquals((byte) (-11), byte0);
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      byte byte0 = Conversion.binaryToByte(booleanArray0, (-2261), (byte) (-86), 95, (-2261));
      assertEquals((byte) (-86), byte0);
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 102, (byte)0, (-1889), 54);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 102
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, (-2251), (short)0, 2, 6);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2251
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, 869000, (short)1590, 868990, 53);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      short short0 = Conversion.binaryToShort(booleanArray0, 0, (byte) (-123), 0, (byte) (-123));
      assertEquals((short) (-123), short0);
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      short short0 = Conversion.binaryToShort(booleanArray0, (-498), (short)1491, (-498), 0);
      assertEquals((short)1491, short0);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, (byte)0, 12, (byte)0, 1482);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      int int0 = Conversion.binaryToInt(booleanArray0, 1, 1893, (short)1, (short) (-327));
      assertEquals(1893, int0);
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      int int0 = Conversion.binaryToInt(booleanArray0, 0, 0, 0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[1] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 1, (byte)14, (-3089), (byte)27);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      long long0 = Conversion.binaryToLong(booleanArray0, (-1), (-1), 0, (-1));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      long long0 = Conversion.binaryToLong(booleanArray0, (-1), 547L, (-1), 0);
      assertEquals(547L, long0);
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, (short)1773, 1L, (-1), 255);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      long long0 = Conversion.binaryToLong(booleanArray0, (short)0, 1L, 51, 495);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("[Z-&969x", 102, (byte) (-123), (byte)107, (byte) (-123));
      assertEquals((byte) (-123), byte0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", 412, (byte)1, (-1329), 0);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("nBools-1+dstPos is greater or equal to than 64", 2352, (byte)9, 2352, 4);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("LWY@KJ B}|jx4wV$$q^", (-1928), (short)74, 1, (short)74);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      short short0 = Conversion.hexToShort("", (-1211), (short)2, 1210, (-2802));
      assertEquals((short)2, short0);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("", 297, 8, 8, 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("(nShorts-1)*16+dstPos is greater or equal to than 64", (-812), 1, 1, 297);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      int int0 = Conversion.hexToInt("", (byte)49, (-1), (-3392), (-1825));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      long long0 = Conversion.hexToLong(" c]t 8F$`r", 2844, (-362L), (byte) (-78), (byte) (-78));
      assertEquals((-362L), long0);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong("2!rdk[?M", (-92), 2067L, (short)54, 3633);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      long long0 = Conversion.hexToLong("", 87, 87, 4, 0);
      assertEquals(87L, long0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong(" c]t 8F$`r", 11, (-5590L), (-1947), 3);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      short short0 = Conversion.byteArrayToShort(byteArray0, 12, (byte) (-20), 3389, (-1546));
      assertEquals((short) (-20), short0);
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, (byte)84, (short)57, 0, (short)57);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      int int0 = Conversion.byteArrayToInt(byteArray0, (short)1, (short)0, (short)0, (short)0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, (byte)1, (-1211), (-39), 76);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (-1211));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1211
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, (-3243), (-3243), 541, 51);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-2056), (-1L), (-2531), (-517));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      short[] shortArray0 = new short[8];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (-2399), 892, (short) (-327), (short)1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2399
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, 2738, 2738, 2738, (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      short[] shortArray0 = new short[8];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short)1, (short)56, (-811), 0);
      assertEquals(56, int0);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      short[] shortArray0 = new short[0];
      int int0 = Conversion.shortArrayToInt(shortArray0, (-1), (-1), (-1), (-1));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      short[] shortArray0 = new short[6];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, (short) (-178), (short) (-180), (short)10, 2);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -178
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, (-1), 0L, 8, 8);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      short[] shortArray0 = new short[0];
      long long0 = Conversion.shortArrayToLong(shortArray0, 0, 0L, 1683, 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      short[] shortArray0 = new short[0];
      long long0 = Conversion.shortArrayToLong(shortArray0, (-1710), (-1710), (-1710), (-1710));
      assertEquals((-1710L), long0);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      short[] shortArray0 = new short[1];
      long long0 = Conversion.shortArrayToLong(shortArray0, (byte)0, (byte)14, (-1), (byte)0);
      assertEquals(14L, long0);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      int[] intArray0 = new int[7];
      long long0 = Conversion.intArrayToLong(intArray0, (-1036), 85, 1732, 0);
      assertEquals(85L, long0);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      int[] intArray0 = new int[0];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, 93, 93, (short)3242, (short)3242);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      int[] intArray0 = new int[1];
      long long0 = Conversion.intArrayToLong(intArray0, (-497), 0L, (-373), (-497));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(9);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(7);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0((byte)1);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigitMsb0((byte) (-87));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: -87
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigit(3549);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: 3549
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(9);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1647));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1644
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[0] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('k');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'k' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('\\');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '\\' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('S');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'S' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('Q');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Q' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('H');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'H' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('C');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('B');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('9');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('3');
      assertTrue(Arrays.equals(new boolean[] {false, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('^');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '^' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('0');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('v');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'v' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('d');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('U');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'U' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('B');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('3');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('>');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '>' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('0');
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('{');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '{' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('b');
      assertEquals(13, int0);
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('I');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'I' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('V');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'V' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('B');
      assertEquals(13, int0);
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToInt('p');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'p' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('0');
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=1, srcPos=0
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      Conversion conversion0 = new Conversion();
  }
}
