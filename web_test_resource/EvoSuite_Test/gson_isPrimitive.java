/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:48:25 GMT 2025
 */

package com.google.gson.internal;

import org.junit.Test;
import static org.junit.Assert.*;
import com.google.gson.internal.Primitives;
import java.lang.reflect.Type;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Primitives_ESTest extends Primitives_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test0()  throws Throwable  {
      Class<Character> class0 = Primitives.wrap((Class<Character>) null);
      assertNull(class0);
  }

  @Test(timeout = 4000)
  public void test1()  throws Throwable  {
      Class<Object> class0 = Object.class;
      Class<Object> class1 = Primitives.unwrap(class0);
      assertFalse(class1.isPrimitive());
  }

  @Test(timeout = 4000)
  public void test2()  throws Throwable  {
      Class<Object> class0 = Primitives.unwrap((Class<Object>) null);
      assertNull(class0);
  }

  @Test(timeout = 4000)
  public void test3()  throws Throwable  {
      Class<Object> class0 = Object.class;
      Class<Object> class1 = Primitives.wrap(class0);
      assertEquals(1, class1.getModifiers());
  }

  @Test(timeout = 4000)
  public void test4()  throws Throwable  {
      Class<Character> class0 = Character.class;
      boolean boolean0 = Primitives.isWrapperType(class0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test5()  throws Throwable  {
      Class<Object> class0 = Object.class;
      boolean boolean0 = Primitives.isWrapperType(class0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test6()  throws Throwable  {
      Class<Character> class0 = Character.class;
      Class<Character> class1 = Primitives.unwrap(class0);
      Class<Character> class2 = Primitives.wrap(class1);
      boolean boolean0 = Primitives.isPrimitive(class2);
      assertEquals("class java.lang.Character", class2.toString());
      assertFalse(boolean0);
      assertEquals(1041, class1.getModifiers());
  }

  @Test(timeout = 4000)
  public void test7()  throws Throwable  {
      Class<Character> class0 = Character.class;
      Class<Character> class1 = Primitives.unwrap(class0);
      boolean boolean0 = Primitives.isPrimitive(class1);
      assertEquals("char", class1.toString());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test8()  throws Throwable  {
      boolean boolean0 = Primitives.isPrimitive((Type) null);
      assertFalse(boolean0);
  }
}
