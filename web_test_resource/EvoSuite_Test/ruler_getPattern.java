/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:03:22 GMT 2025
 */

package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;
import software.amazon.event.ruler.AnythingButValuesSet;
import software.amazon.event.ruler.ByteMachine;
import software.amazon.event.ruler.MatchType;
import software.amazon.event.ruler.NameState;
import software.amazon.event.ruler.NameStateWithPattern;
import software.amazon.event.ruler.Patterns;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class NameStateWithPattern_ESTest extends NameStateWithPattern_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      NameState nameState0 = new NameState();
      NameStateWithPattern nameStateWithPattern0 = new NameStateWithPattern(nameState0, (Patterns) null);
      Patterns patterns0 = nameStateWithPattern0.getPattern();
      assertNull(patterns0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Patterns patterns0 = Patterns.existencePatterns();
      ByteMachine byteMachine0 = new ByteMachine();
      NameState nameState0 = byteMachine0.addPattern(patterns0);
      NameStateWithPattern nameStateWithPattern0 = new NameStateWithPattern(nameState0, (Patterns) null);
      // Undeclared exception!
      try { 
        nameStateWithPattern0.equals(nameStateWithPattern0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("software.amazon.event.ruler.NameStateWithPattern", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Patterns patterns0 = Patterns.existencePatterns();
      NameStateWithPattern nameStateWithPattern0 = null;
      try {
        nameStateWithPattern0 = new NameStateWithPattern((NameState) null, patterns0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Patterns patterns0 = Patterns.existencePatterns();
      ByteMachine byteMachine0 = new ByteMachine();
      NameState nameState0 = byteMachine0.addPattern(patterns0);
      NameStateWithPattern nameStateWithPattern0 = new NameStateWithPattern(nameState0, patterns0);
      AnythingButValuesSet anythingButValuesSet0 = Patterns.anythingButPrefix("N");
      NameStateWithPattern nameStateWithPattern1 = new NameStateWithPattern(nameState0, anythingButValuesSet0);
      boolean boolean0 = nameStateWithPattern1.equals(nameStateWithPattern0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Patterns patterns0 = Patterns.existencePatterns();
      ByteMachine byteMachine0 = new ByteMachine();
      NameState nameState0 = new NameState();
      NameState nameState1 = byteMachine0.addPattern(patterns0);
      NameStateWithPattern nameStateWithPattern0 = new NameStateWithPattern(nameState1, patterns0);
      NameStateWithPattern nameStateWithPattern1 = new NameStateWithPattern(nameState0, patterns0);
      boolean boolean0 = nameStateWithPattern1.equals(nameStateWithPattern0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Patterns patterns0 = Patterns.existencePatterns();
      NameState nameState0 = new NameState();
      NameStateWithPattern nameStateWithPattern0 = new NameStateWithPattern(nameState0, patterns0);
      Object object0 = new Object();
      boolean boolean0 = nameStateWithPattern0.equals(object0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Patterns patterns0 = Patterns.existencePatterns();
      ByteMachine byteMachine0 = new ByteMachine();
      NameState nameState0 = byteMachine0.addPattern(patterns0);
      NameStateWithPattern nameStateWithPattern0 = new NameStateWithPattern(nameState0, patterns0);
      boolean boolean0 = nameStateWithPattern0.equals(nameStateWithPattern0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      NameState nameState0 = new NameState();
      Patterns patterns0 = Patterns.existencePatterns();
      NameStateWithPattern nameStateWithPattern0 = new NameStateWithPattern(nameState0, patterns0);
      boolean boolean0 = nameStateWithPattern0.equals((Object) null);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Patterns patterns0 = Patterns.existencePatterns();
      ByteMachine byteMachine0 = new ByteMachine();
      NameState nameState0 = byteMachine0.addPattern(patterns0);
      NameStateWithPattern nameStateWithPattern0 = new NameStateWithPattern(nameState0, patterns0);
      nameStateWithPattern0.hashCode();
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      NameState nameState0 = new NameState();
      Patterns patterns0 = Patterns.absencePatterns();
      NameStateWithPattern nameStateWithPattern0 = new NameStateWithPattern(nameState0, patterns0);
      Patterns patterns1 = nameStateWithPattern0.getPattern();
      assertEquals(MatchType.ABSENT, patterns1.type());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      NameState nameState0 = new NameState();
      NameStateWithPattern nameStateWithPattern0 = new NameStateWithPattern(nameState0, (Patterns) null);
      NameState nameState1 = nameStateWithPattern0.getNameState();
      assertSame(nameState0, nameState1);
  }
}
