/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:16:24 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.PipedOutputStream;
import java.io.PipedReader;
import java.io.PipedWriter;
import java.io.Reader;
import java.nio.charset.Charset;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import javax.sql.rowset.RowSetMetaDataImpl;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.DuplicateHeaderMode;
import org.apache.commons.csv.QuoteMode;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockFileWriter;
import org.evosuite.runtime.mock.java.io.MockPrintStream;
import org.evosuite.runtime.mock.java.io.MockPrintWriter;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVFormat_ESTest extends CSVFormat_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.InformixUnloadCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      Object[] objectArray0 = new Object[5];
      objectArray0[0] = (Object) cSVFormat0;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withIgnoreEmptyLines();
      MockPrintStream mockPrintStream0 = new MockPrintStream("@=;OPZ\u0004F>b/m");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockPrintStream0, false);
      cSVFormat1.printRecord(mockPrintWriter0, objectArray0);
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(false);
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withTrim(true);
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('N');
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('[');
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(true);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(false);
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withTrailingDelimiter(false);
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      Character character0 = new Character('3');
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSystemRecordSeparator();
      assertTrue(cSVFormat1.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withSystemRecordSeparator();
      assertNull(cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(false);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(true);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(false);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("DG}%dVc@");
      assertTrue(cSVFormat1.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("");
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("=3RH?");
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("5%rjpA*@d^#\u0000`B");
      assertNull(cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('I');
      assertEquals("I", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('X');
      assertEquals("X", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('l');
      assertEquals("l", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      assertNotSame(cSVFormat0, cSVFormat1);
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      QuoteMode quoteMode0 = QuoteMode.MINIMAL;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      assertEquals(',', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      assertEquals(",", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      Character character0 = Character.valueOf('9');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withFirstRecordAsHeader();
      Character character0 = Character.valueOf('y');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      Character character0 = Character.valueOf(')');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      Character character0 = new Character('C');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      Character character0 = Character.valueOf(')');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote('S');
      assertEquals('S', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote('/');
      assertEquals('/', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(true);
      CSVFormat cSVFormat2 = cSVFormat1.withQuote('3');
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertEquals('3', (char)cSVFormat2.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withNullString("C'E0WL;Q");
      assertEquals("\n", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("[|OXLWh .Um*[NS'|eE");
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withNullString("N");
      assertTrue(cSVFormat1.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('N');
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces(true);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces(false);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces(true);
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces(true);
      assertTrue(cSVFormat2.getAutoFlush());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces(false);
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(false);
      CSVFormat cSVFormat3 = cSVFormat2.withQuote('t');
      assertEquals('t', (char)cSVFormat3.getQuoteCharacter());
      assertTrue(cSVFormat3.getTrailingDelimiter());
      assertFalse(cSVFormat3.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withIgnoreHeaderCase();
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withIgnoreHeaderCase();
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines(true);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines(true);
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_TSV.withIgnoreEmptyLines(false);
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Object[] objectArray0 = new Object[6];
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withHeaderComments(objectArray0);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      Object[] objectArray0 = new Object[3];
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      String[] stringArray0 = new String[1];
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(stringArray0);
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withHeader((String[]) null);
      assertEquals('|', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((String[]) null);
      assertNull(cSVFormat1.getQuoteMode());
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.equals((Object)cSVFormat1));
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withAutoFlush(true);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSet) null);
      assertEquals("\\N", cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('7');
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSet) null);
      assertFalse(cSVFormat2.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(resultSet0);
      CSVFormat cSVFormat3 = cSVFormat2.withTrailingDelimiter(true);
      assertTrue(cSVFormat3.getIgnoreHeaderCase());
      assertTrue(cSVFormat3.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(resultSet0);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSet) null);
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Class<QuoteMode> class0 = QuoteMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Class<QuoteMode> class0 = QuoteMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      cSVFormat1.hashCode();
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Class<QuoteMode> class0 = QuoteMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withFirstRecordAsHeader();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      Character character0 = Character.valueOf('2');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      Character character0 = Character.valueOf('a');
      CSVFormat cSVFormat2 = cSVFormat1.withEscape(character0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      Character character0 = new Character('\\');
      CSVFormat cSVFormat2 = cSVFormat1.withEscape(character0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withEscape((Character) null);
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withEscape('@');
      assertEquals('@', (char)cSVFormat2.getEscapeCharacter());
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withEscape('I');
      assertEquals('I', (char)cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('S');
      assertEquals("S", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('n');
      assertEquals("n", cSVFormat2.getDelimiterString());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat3 = cSVFormat2.withDelimiter('y');
      assertTrue(cSVFormat3.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat3.getIgnoreEmptyLines());
      assertEquals('y', cSVFormat3.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Character character0 = new Character('`');
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withCommentMarker(character0);
      assertNotSame(cSVFormat0, cSVFormat1);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('#');
      assertEquals(',', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('#');
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('P');
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('7');
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines(true);
      assertTrue(cSVFormat2.getAutoFlush());
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withAutoFlush(false);
      assertFalse(cSVFormat2.getAutoFlush());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withAutoFlush(true);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withAllowMissingColumnNames(true);
      assertTrue(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames(false);
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames(true);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames(false);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withAllowMissingColumnNames(true);
      assertTrue(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('[');
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      assertTrue(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat1.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('N');
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames();
      CSVFormat cSVFormat3 = cSVFormat2.withRecordSeparator('N');
      assertEquals("N", cSVFormat3.getRecordSeparator());
      assertEquals('N', (char)cSVFormat3.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames();
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      String string0 = cSVFormat0.RFC4180.trim((String) null);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      PipedWriter pipedWriter0 = new PipedWriter();
      PipedReader pipedReader0 = new PipedReader(pipedWriter0);
      CSVParser cSVParser0 = cSVFormat0.MONGODB_TSV.parse(pipedReader0);
      assertFalse(cSVParser0.hasHeaderComment());
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      Character character0 = cSVFormat0.getEscapeCharacter();
      assertNull(character0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      boolean boolean0 = cSVFormat0.getAllowMissingColumnNames();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      Object[] objectArray0 = new Object[0];
      String string0 = cSVFormat0.format(objectArray0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('S');
      cSVFormat0.MYSQL.copy();
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertEquals("S", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.copy();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      CSVFormat[] cSVFormatArray0 = CSVFormat.clone((CSVFormat[]) null);
      assertNull(cSVFormatArray0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Object[] objectArray0 = new Object[4];
      // Undeclared exception!
      try { 
        cSVFormat0.printRecord((Appendable) null, objectArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Charset charset0 = Charset.defaultCharset();
      // Undeclared exception!
      try { 
        cSVFormat0.print((File) null, charset0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.io.File", e);
      }
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      Charset charset0 = Charset.defaultCharset();
      MockFile mockFile0 = new MockFile("");
      try { 
        cSVFormat0.print((File) mockFile0, charset0);
        fail("Expecting exception: FileNotFoundException");
      
      } catch(FileNotFoundException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockFileOutputStream", e);
      }
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.InformixUnloadCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      Object[] objectArray0 = new Object[5];
      objectArray0[2] = (Object) "@=;OPZ\u0004F>b/m";
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withIgnoreEmptyLines();
      assertFalse(cSVFormat1.isQuoteCharacterSet());
      
      MockPrintStream mockPrintStream0 = new MockPrintStream("@=;OPZ\u0004F>b/m");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockPrintStream0, false);
      cSVFormat1.printRecord(mockPrintWriter0, objectArray0);
      assertTrue(cSVFormat1.isEscapeCharacterSet());
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      // Undeclared exception!
      try { 
        cSVFormat0.println((Appendable) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      // Undeclared exception!
      try { 
        cSVFormat0.print((Object) cSVFormat0, (Appendable) null, false);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      boolean boolean0 = cSVFormat0.isQuoteCharacterSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Character character0 = new Character('>');
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withCommentMarker(character0);
      boolean boolean0 = cSVFormat1.isQuoteCharacterSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.isNullStringSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames(true);
      boolean boolean0 = cSVFormat1.isEscapeCharacterSet();
      assertTrue(boolean0);
      assertTrue(cSVFormat1.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      boolean boolean0 = cSVFormat0.isEscapeCharacterSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      String[] stringArray0 = cSVFormat0.getHeader();
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      String[] stringArray0 = cSVFormat1.getHeader();
      assertEquals(0, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Character character0 = Character.valueOf('{');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote(character0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSetMetaData) null);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(resultSet0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Class<QuoteMode> class0 = QuoteMode.class;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(class0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter("format");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Character character0 = Character.valueOf('{');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker(character0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setNullString((String) null);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setSkipHeaderRecord(true);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator('d');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrim(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Character character0 = cSVFormat0.getQuoteCharacter();
      assertEquals('\"', (char)character0);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      String[] stringArray0 = new String[1];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(stringArray0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.InformixUnloadCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      String string0 = cSVFormat0.getDelimiterString();
      assertEquals(",", string0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAllowMissingColumnNames(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAutoFlush(true);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape('h');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote('D');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreHeaderCase(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter('N');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreEmptyLines(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.getIgnoreHeaderCase();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreSurroundingSpaces(true);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrailingDelimiter(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      boolean boolean0 = cSVFormat0.getAutoFlush();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      DuplicateHeaderMode duplicateHeaderMode0 = DuplicateHeaderMode.ALLOW_EMPTY;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDuplicateHeaderMode(duplicateHeaderMode0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker('5');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuoteMode(quoteMode0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      boolean boolean0 = cSVFormat0.getTrim();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator((String) null);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.getIgnoreSurroundingSpaces();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.getIgnoreEmptyLines();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames(false);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      QuoteMode quoteMode0 = QuoteMode.NONE;
      // Undeclared exception!
      try { 
        cSVFormat0.withQuoteMode(quoteMode0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No quotes mode set but no escape character is set
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withEscape('p');
      // Undeclared exception!
      try { 
        cSVFormat1.withCommentMarker('p');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start and the escape character cannot be the same ('p')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      Character character0 = Character.valueOf('W');
      CSVFormat cSVFormat1 = cSVFormat0.withQuote(character0);
      // Undeclared exception!
      try { 
        cSVFormat1.withCommentMarker(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the quoteChar cannot be the same ('W')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('S');
      Character character0 = new Character('S');
      // Undeclared exception!
      try { 
        cSVFormat0.withEscape(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The escape character and the delimiter cannot be the same ('S')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      String string0 = cSVFormat0.trim(",");
      assertEquals(",", string0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withFirstRecordAsHeader();
      Object[] objectArray0 = new Object[4];
      objectArray0[0] = (Object) cSVFormat1;
      String[] stringArray0 = CSVFormat.toStringArray(objectArray0);
      assertEquals(4, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat1);
      Object[] objectArray0 = new Object[4];
      objectArray0[2] = (Object) cSVFormat1;
      cSVFormat_Builder0.setHeaderComments(objectArray0);
      assertTrue(cSVFormat1.isEscapeCharacterSet());
      assertTrue(cSVFormat1.isQuoteCharacterSet());
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.isCommentMarkerSet());
      assertTrue(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat0.equals((Object)cSVFormat1));
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = CSVFormat.newFormat('@');
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      
      Object[] objectArray0 = new Object[4];
      objectArray0[1] = (Object) cSVFormat1;
      CSVFormat cSVFormat2 = cSVFormat0.withHeaderComments(objectArray0);
      assertFalse(cSVFormat2.isCommentMarkerSet());
      assertFalse(cSVFormat2.isNullStringSet());
      assertTrue(cSVFormat2.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      String string0 = cSVFormat0.MYSQL.toString();
      assertEquals("Delimiter=<\t> Escape=<\\> QuoteMode=<ALL_NON_NULL> NullString=<\\N> RecordSeparator=<\n> SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Object[] objectArray0 = new Object[9];
      objectArray0[0] = (Object) "5%rjpA*@d^#\u0000`B";
      String string0 = cSVFormat0.format(objectArray0);
      assertEquals("\"5%rjpA*@d^#\u0000`B\",,,,,,,,", string0);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      String[] stringArray0 = new String[6];
      stringArray0[3] = "W@j>S[3XemY30G()3D2";
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote('S');
      String string0 = cSVFormat1.format(stringArray0);
      assertEquals("\\N\t\\N\t\\N\tSW@j>\\S[3XemY30G()3D2S\t\\N\t\\N", string0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames(true);
      Object[] objectArray0 = new Object[9];
      objectArray0[0] = (Object) "5%rjpA*@d^#\u0000`B";
      String string0 = cSVFormat1.format(objectArray0);
      assertTrue(cSVFormat1.isQuoteCharacterSet());
      assertTrue(cSVFormat1.isEscapeCharacterSet());
      assertEquals("5%rjpA*@d^#\u0000`B||||||||", string0);
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat1.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      CSVFormat.Predefined[] cSVFormat_PredefinedArray0 = CSVFormat.Predefined.values();
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('g');
      assertFalse(cSVFormat1.isQuoteCharacterSet());
      
      cSVFormat1.format(cSVFormat_PredefinedArray0);
      assertEquals("g", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      MockFileWriter mockFileWriter0 = new MockFileWriter("hDM!Z% w|nh0 +6.G");
      Object[] objectArray0 = new Object[3];
      cSVFormat0.printRecord(mockFileWriter0, objectArray0);
      assertFalse(cSVFormat0.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      MockFileWriter mockFileWriter0 = new MockFileWriter("5%rjpA*@d^#\u0000`B", false);
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFileWriter0);
      CSVFormat cSVFormat0 = CSVFormat.newFormat('7');
      cSVFormat0.println(mockPrintWriter0);
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertEquals('7', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      boolean boolean0 = cSVFormat0.isNullStringSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.InformixUnloadCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      assertFalse(cSVFormat0.isEscapeCharacterSet());
      
      Object[] objectArray0 = new Object[5];
      objectArray0[1] = (Object) ",";
      String string0 = cSVFormat0.format(objectArray0);
      assertEquals(",\",\",,,", string0);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      Object[] objectArray0 = new Object[0];
      String string0 = cSVFormat1.format(objectArray0);
      assertEquals("\r\n", string0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.InformixUnloadCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      boolean boolean0 = cSVFormat0.getAllowDuplicateHeaderNames();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('R');
      // Undeclared exception!
      try { 
        cSVFormat0.format((Object[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(true);
      boolean boolean0 = cSVFormat1.equals(cSVFormat0);
      assertFalse(cSVFormat0.equals((Object)cSVFormat1));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames(true);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      cSVFormat1.equals(cSVFormat2);
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat1.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      QuoteMode quoteMode0 = QuoteMode.ALL;
      boolean boolean0 = cSVFormat0.TDF.equals(quoteMode0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      boolean boolean0 = cSVFormat0.equals(cSVFormat1);
      assertTrue(cSVFormat1.getSkipHeaderRecord());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape((Character) null);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("=3RH?");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank((String) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('R');
      // Undeclared exception!
      try { 
        cSVFormat0.withQuote('R');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The quoteChar character and the delimiter cannot be the same ('R')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      ResultSetMetaData resultSetMetaData0 = mock(ResultSetMetaData.class, new ViolatedAssumptionAnswer());
      doReturn(64).when(resultSetMetaData0).getColumnCount();
      doReturn((String) null, (String) null, (String) null, (String) null, (String) null).when(resultSetMetaData0).getColumnLabel(anyInt());
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn(resultSetMetaData0).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(resultSet0);
      assertEquals("\n", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSet) null);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      // Undeclared exception!
      try { 
        cSVFormat_Builder0.setDelimiter("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The delimiter cannot be empty
         //
         verifyException("org.apache.commons.csv.CSVFormat$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.PostgreSQLText;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(';');
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertEquals(';', (char)cSVFormat2.getCommentMarker());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      String string0 = cSVFormat0.getNullString();
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      String string0 = cSVFormat0.toString();
      assertEquals("Delimiter=<,> QuoteChar=<\"> RecordSeparator=<\n> EmptyLines:ignored SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      MockFile mockFile0 = new MockFile("QuoteMode=<", "");
      Charset charset0 = Charset.defaultCharset();
      CSVPrinter cSVPrinter0 = cSVFormat0.print((File) mockFile0, charset0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      DuplicateHeaderMode duplicateHeaderMode0 = cSVFormat0.getDuplicateHeaderMode();
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, duplicateHeaderMode0);
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      // Undeclared exception!
      try { 
        CSVFormat.valueOf("CSVParser has been closed");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.csv.CSVFormat.Predefined.CSVParser has been closed
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('Q');
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertEquals("Q", cSVFormat2.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      PipedOutputStream pipedOutputStream0 = new PipedOutputStream();
      MockPrintStream mockPrintStream0 = new MockPrintStream(pipedOutputStream0);
      CSVPrinter cSVPrinter0 = cSVFormat0.print((Appendable) mockPrintStream0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      MockFile mockFile0 = new MockFile("org.apache.commons.csv.DuplicateHeaderMode");
      MockPrintStream mockPrintStream0 = new MockPrintStream(mockFile0);
      cSVFormat0.print((Object) quoteMode0, (Appendable) mockPrintStream0, true);
      assertEquals(43L, mockFile0.length());
      assertTrue(cSVFormat0.isEscapeCharacterSet());
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Character character0 = cSVFormat0.getEscapeCharacter();
      assertEquals('\\', (char)character0);
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      Character character0 = cSVFormat0.getCommentMarker();
      assertNull(character0);
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.getAllowMissingColumnNames();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat(')');
      // Undeclared exception!
      try { 
        cSVFormat0.parse((Reader) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // reader
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Character character0 = Character.valueOf('l');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('l');
      assertEquals("l", cSVFormat2.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Character character0 = Character.valueOf('^');
      CSVFormat cSVFormat1 = cSVFormat0.withQuote(character0);
      assertEquals('^', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Character character0 = Character.valueOf('-');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      assertNull(cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      Class<QuoteMode> class0 = QuoteMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVPrinter cSVPrinter0 = cSVFormat0.printer();
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      boolean boolean0 = cSVFormat0.getSkipHeaderRecord();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      char char0 = cSVFormat0.getDelimiter();
      assertEquals(',', char0);
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withAutoFlush(true);
      assertTrue(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      assertTrue(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('#');
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      CSVFormat cSVFormat3 = cSVFormat2.withSkipHeaderRecord();
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertEquals('#', (char)cSVFormat3.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test219()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(false);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test220()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test221()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('#');
      assertEquals('#', (char)cSVFormat2.getCommentMarker());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test222()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      String[] stringArray0 = new String[2];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeaderComments(stringArray0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test223()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("[|OXLWh .Um*[NS'|eE");
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test224()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments((Object[]) null);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test225()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }
}
