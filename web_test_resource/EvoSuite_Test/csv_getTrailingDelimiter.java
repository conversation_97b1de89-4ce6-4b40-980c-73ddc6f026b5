/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:22:18 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.File;
import java.io.OutputStreamWriter;
import java.nio.CharBuffer;
import java.nio.ReadOnlyBufferException;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import javax.sql.rowset.RowSetMetaDataImpl;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.DuplicateHeaderMode;
import org.apache.commons.csv.QuoteMode;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockFileOutputStream;
import org.evosuite.runtime.mock.java.io.MockPrintStream;
import org.evosuite.runtime.mock.java.io.MockPrintWriter;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVFormat_ESTest extends CSVFormat_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      cSVFormat1.hashCode();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(false);
      assertFalse(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(true);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('^');
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getTrim());
      assertEquals('^', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('s');
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(false);
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertEquals('s', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(false);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('o');
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      assertEquals('o', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withSystemRecordSeparator();
      assertEquals('\t', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withSystemRecordSeparator();
      assertFalse(cSVFormat1.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat(',');
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      assertEquals(",", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(true);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(true);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(false);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.DEFAULT.withSystemRecordSeparator();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("ALLOW_EMPTY");
      assertFalse(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("MongoDBCsv");
      assertEquals(',', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('x');
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertEquals("x", cSVFormat2.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withRecordSeparator('|');
      assertEquals("|", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(true);
      QuoteMode quoteMode0 = QuoteMode.MINIMAL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.DEFAULT.withQuote((Character) null);
      assertNull(cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      Character character0 = new Character('q');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Character character0 = new Character('J');
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withQuote(character0);
      assertTrue(cSVFormat1.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      Character character0 = new Character('q');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote('y');
      assertEquals('y', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("");
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("");
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces(false);
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreSurroundingSpaces(true);
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("");
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      CSVFormat cSVFormat3 = cSVFormat2.withIgnoreHeaderCase();
      assertTrue(cSVFormat3.getIgnoreHeaderCase());
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertTrue(cSVFormat3.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(true);
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setAutoFlush(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getAutoFlush());
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withNullString(":(.hl';)7MlYq");
      String[] stringArray0 = new String[1];
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(stringArray0);
      assertTrue(cSVFormat2.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(true);
      String[] stringArray0 = new String[1];
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(stringArray0);
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      String[] stringArray0 = new String[9];
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(stringArray0);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(false);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat3 = cSVFormat2.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertFalse(cSVFormat3.getAllowDuplicateHeaderNames());
      assertTrue(cSVFormat3.equals((Object)cSVFormat2));
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('F');
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      cSVFormat0.MYSQL.withHeader(resultSet0);
      assertFalse(cSVFormat0.getAutoFlush());
      assertEquals('F', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(resultSet0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      Class<QuoteMode> class0 = QuoteMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      Class<QuoteMode> class0 = QuoteMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      Character character0 = new Character('#');
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertEquals(QuoteMode.ALL_NON_NULL, cSVFormat1.getQuoteMode());
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Character character0 = new Character('=');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('s');
      CSVFormat cSVFormat2 = cSVFormat1.withEscape(character0);
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat3 = cSVFormat2.withQuoteMode(quoteMode0);
      assertEquals('s', (char)cSVFormat3.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Oracle;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      Character character0 = Character.valueOf('J');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertTrue(cSVFormat1.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      Character character0 = new Character('f');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withEscape(character0);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withNullString(":(.hl';)7MlYq");
      CSVFormat cSVFormat2 = cSVFormat1.withEscape('/');
      assertEquals('/', (char)cSVFormat2.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('F');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape('P');
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertEquals('P', (char)cSVFormat1.getEscapeCharacter());
      assertFalse(cSVFormat1.getTrim());
      assertEquals('F', cSVFormat1.getDelimiter());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('3');
      assertEquals('3', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = Character.valueOf('T');
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('T');
      assertEquals("T", cSVFormat2.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withAllowMissingColumnNames();
      Character character0 = new Character('=');
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker(character0);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('x');
      assertEquals('x', (char)cSVFormat2.getCommentMarker());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('o');
      assertEquals('o', (char)cSVFormat2.getCommentMarker());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('E');
      assertEquals('E', (char)cSVFormat2.getCommentMarker());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('0');
      assertEquals('0', (char)cSVFormat1.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('o');
      assertEquals('o', (char)cSVFormat2.getCommentMarker());
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withAutoFlush(false);
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(false);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('^');
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertEquals('^', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('_');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertEquals('_', cSVFormat1.getDelimiter());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('_');
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withAllowMissingColumnNames();
      assertTrue(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertEquals('_', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(true);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames();
      assertNull(cSVFormat1.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames();
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      String string0 = cSVFormat0.INFORMIX_UNLOAD_CSV.trim((String) null);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      QuoteMode quoteMode0 = cSVFormat0.getQuoteMode();
      assertEquals(QuoteMode.MINIMAL, quoteMode0);
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      Class<QuoteMode> class0 = QuoteMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      String[] stringArray0 = cSVFormat1.getHeader();
      assertNotNull(stringArray0);
      assertFalse(cSVFormat0.equals((Object)cSVFormat1));
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat3 = cSVFormat2.copy();
      assertTrue(cSVFormat3.getSkipHeaderRecord());
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat3.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat1.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      CSVFormat[] cSVFormatArray0 = new CSVFormat[4];
      CSVFormat[] cSVFormatArray1 = CSVFormat.clone(cSVFormatArray0);
      assertFalse(cSVFormatArray1.equals((Object)cSVFormatArray0));
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat(',');
      Character character0 = Character.valueOf(',');
      // Undeclared exception!
      try { 
        cSVFormat0.withCommentMarker(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the delimiter cannot be the same (',')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('d');
      // Undeclared exception!
      try { 
        cSVFormat0.withCommentMarker('d');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the delimiter cannot be the same ('d')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      char[] charArray0 = new char[4];
      CharBuffer charBuffer0 = CharBuffer.wrap(charArray0);
      CharBuffer charBuffer1 = CharBuffer.wrap((CharSequence) charBuffer0);
      // Undeclared exception!
      try { 
        cSVFormat0.print((Object) cSVFormat0, (Appendable) charBuffer1, true);
        fail("Expecting exception: ReadOnlyBufferException");
      
      } catch(ReadOnlyBufferException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.nio.CharBuffer", e);
      }
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.RFC4180;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      MockFile mockFile0 = new MockFile("QuoteChar=<", "");
      // Undeclared exception!
      try { 
        cSVFormat0.print((File) mockFile0, (Charset) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // charset
         //
         verifyException("java.io.OutputStreamWriter", e);
      }
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Oracle;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      String string0 = cSVFormat1.toString();
      assertEquals("Delimiter=<,> Escape=<\\> QuoteChar=<\"> QuoteMode=<MINIMAL> NullString=<\\N> RecordSeparator=<\n> EmptyLines:ignored SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      // Undeclared exception!
      try { 
        cSVFormat0.MONGODB_CSV.println((Appendable) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      // Undeclared exception!
      try { 
        cSVFormat0.POSTGRESQL_TEXT.print((Object) null, (Appendable) null, false);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      boolean boolean0 = cSVFormat0.isQuoteCharacterSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      boolean boolean0 = cSVFormat0.isCommentMarkerSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      String[] stringArray0 = cSVFormat0.getHeaderComments();
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      String[] stringArray0 = cSVFormat0.getHeader();
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      Character character0 = Character.valueOf('J');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape(character0);
      cSVFormat_Builder1.setDelimiter('J');
      // Undeclared exception!
      try { 
        cSVFormat_Builder0.build();
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The escape character and the delimiter cannot be the same ('J')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Character character0 = Character.valueOf('6');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote(character0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSetMetaData) null);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(resultSet0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter("]W+El");
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker((Character) null);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setSkipHeaderRecord(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator('g');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      cSVFormat0.getQuoteMode();
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrim(true);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      Character character0 = cSVFormat0.getQuoteCharacter();
      assertEquals('\"', (char)character0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('V');
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      String[] stringArray0 = new String[8];
      cSVFormat_Builder0.setHeader(stringArray0);
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertEquals("V", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAllowMissingColumnNames(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('_');
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      cSVFormat_Builder0.setQuote('_');
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertEquals("_", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreHeaderCase(true);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreEmptyLines(true);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreSurroundingSpaces(true);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrailingDelimiter(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      DuplicateHeaderMode duplicateHeaderMode0 = DuplicateHeaderMode.ALLOW_ALL;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDuplicateHeaderMode(duplicateHeaderMode0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator("");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      boolean boolean0 = cSVFormat0.getIgnoreSurroundingSpaces();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      boolean boolean0 = cSVFormat0.getIgnoreEmptyLines();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      QuoteMode quoteMode0 = QuoteMode.NONE;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuoteMode(quoteMode0);
      // Undeclared exception!
      try { 
        cSVFormat_Builder1.build();
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No quotes mode set but no escape character is set
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape('3');
      cSVFormat_Builder1.setCommentMarker('3');
      // Undeclared exception!
      try { 
        cSVFormat_Builder0.build();
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start and the escape character cannot be the same ('3')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('~');
      // Undeclared exception!
      try { 
        cSVFormat1.withQuote('~');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the quoteChar cannot be the same ('~')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('i');
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) null);
      // Undeclared exception!
      try { 
        cSVFormat2.withDelimiter('i');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the delimiter cannot be the same ('i')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.InformixUnload;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('E');
      Character character0 = Character.valueOf('E');
      // Undeclared exception!
      try { 
        cSVFormat1.withEscape(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The escape character and the delimiter cannot be the same ('E')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      String string0 = cSVFormat0.ORACLE.trim("W~)3^ oN{-D");
      assertEquals("W~)3^ oN{-D", string0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      String string0 = cSVFormat0.DEFAULT.trim("");
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      Object[] objectArray0 = new Object[2];
      objectArray0[0] = (Object) cSVFormat0;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD_CSV.withHeaderComments(objectArray0);
      assertFalse(cSVFormat1.isNullStringSet());
      assertTrue(cSVFormat1.isQuoteCharacterSet());
      assertFalse(cSVFormat1.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Character character0 = new Character('q');
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withCommentMarker(character0);
      MockFileOutputStream mockFileOutputStream0 = new MockFileOutputStream("(line ", false);
      OutputStreamWriter outputStreamWriter0 = new OutputStreamWriter(mockFileOutputStream0);
      Object[] objectArray0 = new Object[9];
      objectArray0[0] = (Object) outputStreamWriter0;
      cSVFormat1.printRecord(outputStreamWriter0, objectArray0);
      assertTrue(cSVFormat1.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      MockPrintStream mockPrintStream0 = new MockPrintStream("w<jJ0nIfdC2~Ks$,5n");
      Object[] objectArray0 = new Object[1];
      objectArray0[0] = (Object) mockPrintStream0;
      cSVFormat0.printRecord(mockPrintStream0, objectArray0);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
      assertTrue(cSVFormat0.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      assertFalse(cSVFormat0.isEscapeCharacterSet());
      
      MockFileOutputStream mockFileOutputStream0 = new MockFileOutputStream("(line ", false);
      OutputStreamWriter outputStreamWriter0 = new OutputStreamWriter(mockFileOutputStream0);
      cSVFormat0.print((Object) cSVFormat0, (Appendable) outputStreamWriter0, false);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      MockFileOutputStream mockFileOutputStream0 = new MockFileOutputStream("(line ", false);
      OutputStreamWriter outputStreamWriter0 = new OutputStreamWriter(mockFileOutputStream0);
      Object[] objectArray0 = new Object[9];
      cSVFormat0.printRecord(outputStreamWriter0, objectArray0);
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('V');
      MockFile mockFile0 = new MockFile("~");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFile0);
      cSVFormat0.println(mockPrintWriter0);
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getTrim());
      assertEquals('V', cSVFormat0.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Object[] objectArray0 = new Object[5];
      objectArray0[2] = (Object) "MongoDBCsv";
      String string0 = cSVFormat0.INFORMIX_UNLOAD.format(objectArray0);
      assertFalse(cSVFormat0.isEscapeCharacterSet());
      assertEquals("||MongoDBCsv||", string0);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      boolean boolean0 = cSVFormat0.isQuoteCharacterSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      Object[] objectArray0 = new Object[5];
      objectArray0[1] = (Object) cSVFormat0;
      String[] stringArray0 = CSVFormat.toStringArray(objectArray0);
      assertEquals(5, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withFirstRecordAsHeader();
      Object[] objectArray0 = new Object[5];
      objectArray0[3] = (Object) cSVFormat1;
      String[] stringArray0 = CSVFormat.toStringArray(objectArray0);
      assertEquals(5, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(false);
      String[] stringArray0 = cSVFormat2.getHeader();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
      assertEquals(0, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.RFC4180.equals(cSVFormat0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      Object object0 = new Object();
      boolean boolean0 = cSVFormat0.equals(object0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSet) null);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Oracle;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      String string0 = cSVFormat0.toString();
      assertEquals("Delimiter=<,> Escape=<\\> QuoteChar=<\"> QuoteMode=<MINIMAL> NullString=<\\N> RecordSeparator=<\n> SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreEmptyLines(false);
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('=');
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertEquals('=', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      DuplicateHeaderMode duplicateHeaderMode0 = cSVFormat0.getDuplicateHeaderMode();
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, duplicateHeaderMode0);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(false);
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.valueOf("MongoDBCsv");
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
    Future<?> future = executor.submit(new Runnable(){ 
            @Override public void run() { 
        try {
          CSVFormat cSVFormat0 = CSVFormat.ORACLE;
          MockFile mockFile0 = new MockFile(".E{7/b[G");
          Path path0 = mockFile0.toPath();
          Charset charset0 = Charset.defaultCharset();
          // Undeclared exception!
          try { 
            cSVFormat0.EXCEL.print(path0, charset0);
            fail("Expecting exception: SecurityException");
          
          } catch(SecurityException e) {
             //
             // Security manager blocks (\"java.io.FilePermission\" \".E{7/b[G\" \"write\")
             // java.lang.Thread.getStackTrace(Thread.java:1559)
             // org.evosuite.runtime.sandbox.MSecurityManager.checkPermission(MSecurityManager.java:424)
             // java.lang.SecurityManager.checkWrite(SecurityManager.java:979)
             // sun.nio.fs.UnixChannelFactory.open(UnixChannelFactory.java:247)
             // sun.nio.fs.UnixChannelFactory.newFileChannel(UnixChannelFactory.java:136)
             // sun.nio.fs.UnixChannelFactory.newFileChannel(UnixChannelFactory.java:148)
             // sun.nio.fs.UnixFileSystemProvider.newByteChannel(UnixFileSystemProvider.java:212)
             // java.nio.file.spi.FileSystemProvider.newOutputStream(FileSystemProvider.java:434)
             // java.nio.file.Files.newOutputStream(Files.java:216)
             // java.nio.file.Files.newBufferedWriter(Files.java:2860)
             // org.apache.commons.csv.CSVFormat.print(CSVFormat.java:1886)
             // sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
             // sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
             // sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
             // java.lang.reflect.Method.invoke(Method.java:498)
             // org.evosuite.testcase.statements.MethodStatement$1.execute(MethodStatement.java:256)
             // org.evosuite.testcase.statements.AbstractStatement.exceptionHandler(AbstractStatement.java:165)
             // org.evosuite.testcase.statements.MethodStatement.execute(MethodStatement.java:219)
             // org.evosuite.testcase.execution.TestRunnable.executeStatements(TestRunnable.java:286)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:192)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:49)
             // java.util.concurrent.FutureTask.run(FutureTask.java:266)
             // java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
             // java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
             // java.lang.Thread.run(Thread.java:748)
             //
             verifyException("org.evosuite.runtime.sandbox.MSecurityManager", e);
          }
        } catch(Throwable t) {
            // Need to catch declared exceptions
        }
      } 
    });
    future.get(4000, TimeUnit.MILLISECONDS);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      char[] charArray0 = new char[4];
      CharBuffer charBuffer0 = CharBuffer.wrap(charArray0);
      CSVPrinter cSVPrinter0 = cSVFormat0.print((Appendable) charBuffer0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      assertTrue(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Oracle;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      Character character0 = cSVFormat0.getEscapeCharacter();
      assertEquals('\\', (char)character0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      boolean boolean0 = cSVFormat0.getAllowMissingColumnNames();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Excel;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(resultSet0);
      assertEquals('\t', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Character character0 = new Character('=');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('_');
      CSVFormat cSVFormat1 = cSVFormat0.RFC4180.withAllowDuplicateHeaderNames(true);
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat1.getDuplicateHeaderMode());
      assertEquals('_', cSVFormat0.getDelimiter());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Excel;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVPrinter cSVPrinter0 = cSVFormat0.printer();
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Excel;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      MockFile mockFile0 = new MockFile("$VALUES", "$VALUES");
      Charset charset0 = Charset.defaultCharset();
      CSVPrinter cSVPrinter0 = cSVFormat0.EXCEL.print((File) mockFile0, charset0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      boolean boolean0 = cSVFormat0.getSkipHeaderRecord();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      char char0 = cSVFormat0.getDelimiter();
      assertEquals(',', char0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('|');
      assertEquals("|", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(false);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('J');
      CSVFormat cSVFormat3 = cSVFormat2.withIgnoreEmptyLines();
      assertTrue(cSVFormat3.getTrailingDelimiter());
      assertTrue(cSVFormat3.getIgnoreEmptyLines());
      assertEquals('J', cSVFormat3.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote('J');
      assertEquals('J', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      String[] stringArray0 = new String[0];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeaderComments(stringArray0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(true);
      CSVFormat cSVFormat2 = cSVFormat1.withEscape('/');
      assertTrue(cSVFormat2.getTrim());
      assertEquals('/', (char)cSVFormat2.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withAutoFlush(false);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("\"");
      assertFalse(cSVFormat1.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setNullString("org.apache.commons.csv.DuplicateHeaderMode");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      String[] stringArray0 = new String[0];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeaderComments((Object[]) stringArray0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('J');
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      // Undeclared exception!
      try { 
        cSVFormat2.withQuote('J');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The quoteChar character and the delimiter cannot be the same ('J')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      assertEquals("\r\n", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Oracle;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD_CSV.withIgnoreHeaderCase();
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }
}
