/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:53:06 GMT 2025
 */

package com.google.gson;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import com.google.gson.FormattingStyle;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class FormattingStyle_ESTest extends FormattingStyle_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      FormattingStyle formattingStyle1 = formattingStyle0.withSpaceAfterSeparators(false);
      assertFalse(formattingStyle1.usesSpaceAfterSeparators());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      FormattingStyle formattingStyle1 = formattingStyle0.withIndent("");
      assertFalse(formattingStyle1.usesSpaceAfterSeparators());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.PRETTY;
      boolean boolean0 = formattingStyle0.usesSpaceAfterSeparators();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      FormattingStyle formattingStyle1 = formattingStyle0.PRETTY.withSpaceAfterSeparators(true);
      String string0 = formattingStyle1.getNewline();
      assertTrue(formattingStyle1.usesSpaceAfterSeparators());
      assertEquals("  ", formattingStyle1.getIndent());
      assertEquals("\n", string0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      FormattingStyle formattingStyle1 = formattingStyle0.PRETTY.withSpaceAfterSeparators(true);
      String string0 = formattingStyle1.getIndent();
      assertTrue(formattingStyle1.usesSpaceAfterSeparators());
      assertEquals("  ", string0);
      assertEquals("\n", formattingStyle1.getNewline());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      // Undeclared exception!
      try { 
        formattingStyle0.withNewline((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // newline == null
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      // Undeclared exception!
      try { 
        formattingStyle0.withIndent((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // indent == null
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      // Undeclared exception!
      try { 
        formattingStyle0.withNewline("QQ;l?1_;a");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Only combinations of \\n and \\r are allowed in newline.
         //
         verifyException("com.google.gson.FormattingStyle", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      String string0 = formattingStyle0.getNewline();
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      FormattingStyle formattingStyle1 = formattingStyle0.withNewline("");
      assertFalse(formattingStyle1.usesSpaceAfterSeparators());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.PRETTY;
      // Undeclared exception!
      try { 
        formattingStyle0.withIndent(">|E~V}.3q(?");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Only combinations of spaces and tabs are allowed in indent.
         //
         verifyException("com.google.gson.FormattingStyle", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      boolean boolean0 = formattingStyle0.usesSpaceAfterSeparators();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      String string0 = formattingStyle0.getIndent();
      assertEquals("", string0);
  }
}
