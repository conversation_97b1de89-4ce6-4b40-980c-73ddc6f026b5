/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:24:02 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.Conversion;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.util.MockUUID;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Conversion_ESTest extends Conversion_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)0, (short)0, booleanArray0, 1, 1);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.longToBinary((-1L), 1, booleanArray0, 1, 95);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      byteArray0[0] = (byte)95;
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, 0, 0, (-1544), (byte)95);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 6
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, 2451, 0);
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)0, (-1), "{8\"5WtN)", (-1), (short)0);
      assertEquals("{8\"5WtN)", string0);
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      byte[] byteArray0 = Conversion.shortToByteArray((short) (-3716), (short) (-3716), (byte[]) null, (byte)1, (short) (-3716));
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.shortToBinary((short)1426, (-2117), (boolean[]) null, (-2994), (-904));
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      short[] shortArray0 = new short[3];
      long long0 = Conversion.shortArrayToLong(shortArray0, 77, (-861L), 643, (-3098));
      assertEquals((-861L), long0);
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      short[] shortArray0 = new short[7];
      short[] shortArray1 = Conversion.longToShortArray(2451, 2451, shortArray0, 85, (-2581));
      assertSame(shortArray1, shortArray0);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      int[] intArray0 = new int[0];
      int[] intArray1 = Conversion.longToIntArray(2106L, 1, intArray0, 0, (-2215));
      assertEquals(0, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      short[] shortArray0 = new short[6];
      short[] shortArray1 = Conversion.intToShortArray(64, (byte) (-13), shortArray0, (byte)0, (short)0);
      assertSame(shortArray1, shortArray0);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(9);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.intToBinary((-5232), 0, booleanArray0, (-448), (-1));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      int[] intArray0 = new int[2];
      long long0 = Conversion.intArrayToLong(intArray0, (-1), (-437L), 2841, 0);
      assertEquals((-437L), long0);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      short short0 = Conversion.hexToShort("OG~ii4I'lqzKLHVWO", 1, (byte) (-64), 0, (-3616));
      assertEquals((short) (-64), short0);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      long long0 = Conversion.hexToLong("lY&;+,dg-", (-546), 0L, 0, 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      long long0 = Conversion.hexToLong(".-DgZRVA=Gxy[>", (byte) (-64), (-2284L), 99, 0);
      assertEquals((-2284L), long0);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      int int0 = Conversion.hexToInt(".yf_]m%nel", 4, 4, (-2240), (-2240));
      assertEquals(4, int0);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      int int0 = Conversion.hexToInt("p'v-osiR%l>", 86, (-2608), 182, (short)0);
      assertEquals((-2608), int0);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)111, (byte)101, "", (byte)101, (-424));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      long long0 = Conversion.byteArrayToLong(byteArray0, 32, 0L, (-2352), (-627));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-1860), (-3666L), 0, (short) (-4549));
      assertEquals((-3666L), long0);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      int int0 = Conversion.byteArrayToInt(byteArray0, (byte)67, (byte)67, (byte)89, (-2442));
      assertEquals(67, int0);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      int int0 = Conversion.binaryToInt(booleanArray0, (-32), 816, (byte)0, 0);
      assertEquals(816, int0);
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 4035, (byte)0, 0, (-191));
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      byte byte0 = Conversion.binaryToByte(booleanArray0, (-4853), (byte) (-109), 98, (-4853));
      assertEquals((byte) (-109), byte0);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short) (-286), (short) (-286), (String) null, (short) (-286), (short) (-286));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt((short[]) null, 850, (short)0, 4463, (-1864));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex((-4612), (byte) (-33), (String) null, 66, (-2282));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt((String) null, (-693), (-693), (-693), 75);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)97, (-2994), (String) null, (-3037), (byte)97);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid((byte[]) null, 4674);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong((byte[]) null, 0, 0L, 2452, (-1544));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToShort((boolean[]) null, (byte)79, (byte)0, (-1860), 80);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToLong((boolean[]) null, 0, 0L, 2809, 2809);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToInt((boolean[]) null, (byte) (-117), (-1643), (-262), (-1643));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null, 1783);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1361));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1358
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null, (-975));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToByte((boolean[]) null, (short)245, (byte) (-68), (-1), 85);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit((boolean[]) null, 0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 290);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byte[] byteArray1 = Conversion.longToByteArray(102, 778, byteArray0, (-9), (-421));
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(14);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 52);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=8, srcPos=52
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-257));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToInt('=');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '=' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, 51);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Need at least 16 bytes for UUID
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (-538));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -538
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("Py2~`Lsv");
      byte[] byteArray0 = new byte[6];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, (byte)0, 2254);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBytes is greater than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("");
      byte[] byteArray0 = new byte[5];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (byte)0, (byte)0);
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)89, (-768), booleanArray0, (-768), 98);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -768
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte)54, (short)15, booleanArray0, 96, (-1431));
      assertEquals(0, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)60, 102, booleanArray0, 102, 3744);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte)0, (-11204), booleanArray0, (-1872), (byte)0);
      assertEquals(2, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('B');
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)93, (-2576), booleanArray0, (byte)93, 87);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 93
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short) (-2442), (-8998), booleanArray0, 774, 57);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 774
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short) (-2843), 64, booleanArray0, 51, 85);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)8, 87, booleanArray0, (short) (-109), (byte)0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(0, (-1583), booleanArray0, (short)9, 51);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((-1923), (-4713), booleanArray0, (-3046), 53);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -3046
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.intToBinary(0, 0, (boolean[]) null, 0, (-546));
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((-1554), 101, booleanArray0, 101, 101);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.longToBinary(3151L, (short) (-397), booleanArray0, (-917), (-1636));
      assertTrue(Arrays.equals(new boolean[] {}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      boolean[] booleanArray1 = Conversion.longToBinary((-10L), 86, booleanArray0, (-2080), (byte)0);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false, false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)120, 1, "(nHexs-1)*4+srcPos is greater or equal to than 32", 1, 1);
      assertEquals("(cHexs-1)*4+srcPos is greater or equal to than 32", string0);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte) (-96), (byte)0, "M)+A0q", 1716, 816);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte) (-62), (-909), "oAzq3|EfivEyY+;JJG", (-603), 0);
      assertEquals("oAzq3|EfivEyY+;JJG", string0);
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)0, (-1589), "", (-394), (-1));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      String string0 = Conversion.shortToHex((byte)52, 89, (String) null, 9, 0);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)992, (short)992, "laCKxHfIAD^/V\"9", (short)992, 4164);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex((-3401), (-3256), ")`", 754, 613);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 754
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(2738, 1930, "", 271, 56);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      String string0 = Conversion.intToHex((-1778), (-2205), "nBools-1+dstPos is greater or equal to than 8", (-3401), 0);
      assertEquals("nBools-1+dstPos is greater or equal to than 8", string0);
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      String string0 = Conversion.intToHex(55, 16, "", 1, (-3318));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(1311L, 56, "/'unh%[Sqs\"cxgcZhTz", 51, 56);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      String string0 = Conversion.longToHex(0L, 0, "", 0, 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      String string0 = Conversion.longToHex((-957L), 56, "lr)8/z", 255, (-419));
      assertEquals("lr)8/z", string0);
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short)281, 64, byteArray0, (short)281, (byte)95);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byte[] byteArray1 = Conversion.shortToByteArray((short) (-1), 0, byteArray0, (byte) (-33), (-1));
      assertEquals(3, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray(5, (-257), byteArray0, 2425, 7);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2425
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray((short)1028, (-504), byteArray0, (-1), 80);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      byte[] byteArray1 = Conversion.intToByteArray((-140), (-140), byteArray0, 0, 0);
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.intToByteArray((byte)0, (byte)0, byteArray0, (byte)0, (-603));
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("x)OGd.(q)B*r(DE|X");
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, 778, (-2625));
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.longToByteArray((-1532L), 307, byteArray0, 822, 0);
      assertArrayEquals(new byte[] {(byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray(0L, (-46), byteArray0, 0, 910);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray(3202, 4463, shortArray0, 1, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      short[] shortArray0 = Conversion.intToShortArray((short)2768, (-2472), (short[]) null, 0, 0);
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      short[] shortArray0 = Conversion.intToShortArray(480, (short) (-308), (short[]) null, 1098, (short) (-308));
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      short[] shortArray0 = new short[1];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray((byte)34, 102, shortArray0, (byte)92, 102);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      short[] shortArray0 = Conversion.longToShortArray((byte)0, (-1718), (short[]) null, 92, (byte)0);
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      int[] intArray0 = new int[1];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray(0L, (-2586), intArray0, 0, 56);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      int[] intArray0 = new int[7];
      int[] intArray1 = Conversion.longToIntArray(1L, (-3126), intArray0, 2101, (-3126));
      assertSame(intArray0, intArray1);
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      int[] intArray0 = new int[1];
      int[] intArray1 = Conversion.longToIntArray(365, (-1651), intArray0, 1098, 0);
      assertEquals(1, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      int[] intArray0 = new int[1];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray(1098, 52, intArray0, (short) (-308), 99);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, (byte) (-125), (byte)95, (byte) (-125), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -125
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 1, (byte)12, (-1), (byte) (-125));
      assertEquals((byte)12, byte0);
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 1, (byte)112, 96, (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, (-3519), (byte) (-90), (byte) (-90), 29);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -3519
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      short short0 = Conversion.binaryToShort(booleanArray0, 0, (short)463, (-931), 0);
      assertEquals((short)463, short0);
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, (-3519), (byte) (-90), 67, 10);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 676, 2927, (-318), 3201);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      int int0 = Conversion.binaryToInt(booleanArray0, 2853, (short) (-308), 1069, (-2657));
      assertEquals((-308), int0);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      int int0 = Conversion.binaryToInt(booleanArray0, (byte)0, (byte)0, 64, (byte)0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 57, 57, 90, 90);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      long long0 = Conversion.binaryToLong(booleanArray0, 1214, (byte)47, (-28), 0);
      assertEquals(47L, long0);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      long long0 = Conversion.binaryToLong(booleanArray0, (-1388), 1L, (-1388), (-1388));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      long long0 = Conversion.binaryToLong(booleanArray0, (-933), 0L, 100, (-933));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", 0, (byte) (-101), 1, (-16));
      assertEquals((byte) (-101), byte0);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("", 0, (byte)49, (byte)49, 48);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("KE'HJj5Ch]ahSeQ", (-1620), (byte)53, 99, 0);
      assertEquals((byte)53, byte0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort((String) null, 0, (short)0, (-2926), 264);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      short short0 = Conversion.hexToShort("", 3932, (short)197, 290, (-899));
      assertEquals((short)197, short0);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      short short0 = Conversion.hexToShort("", 0, (short)0, (-546), 0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("", 0, (short)0, 1997, 767);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt(".yf_]m%nel", (-2240), (-2240), (-2240), 62);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("nB@ols-o+trcPos is greater or equal to than 32", (-3616), 435, (short)0, 435);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      int int0 = Conversion.hexToInt("nBools-1+srcPos is greater or equal to than 32", (-3616), 0, (short)0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong(".yf_]m%nel", 422, 4, 5, 4);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong((String) null, 3637, 3637, 3637, (-693));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      long long0 = Conversion.hexToLong("", 774, 23L, (-1), 0);
      assertEquals(23L, long0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      long long0 = Conversion.hexToLong("", 774, 51, 774, (-2442));
      assertEquals(51L, long0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, (byte) (-121), (byte)75, 0, (byte)75);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      short short0 = Conversion.byteArrayToShort(byteArray0, 12, (short)71, (-2127), 0);
      assertEquals((short)71, short0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      short short0 = Conversion.byteArrayToShort(byteArray0, 0, (short)0, (-377), 0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      short short0 = Conversion.byteArrayToShort(byteArray0, (byte) (-124), (byte) (-1), (byte) (-113), (-95));
      assertEquals((short) (-1), short0);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, 3248, 352, (-870), 1107);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      int int0 = Conversion.byteArrayToInt(byteArray0, (-3616), (-489), 0, (-489));
      assertEquals((-489), int0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, (byte)1, (-1544), (byte)95, (byte)1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-2597), 5289L, (-2051), (byte)0);
      assertEquals(5289L, long0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (-917), (short) (-397), (-2629), 3);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -917
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      short[] shortArray0 = new short[2];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (short)1942, 2991, 2991, 2991);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      short[] shortArray0 = new short[2];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short)0, 1, (byte)0, (short)0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      short[] shortArray0 = new short[0];
      int int0 = Conversion.shortArrayToInt(shortArray0, 417, 98, 48, (-873));
      assertEquals(98, int0);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      short[] shortArray0 = new short[2];
      int int0 = Conversion.shortArrayToInt(shortArray0, (-1617), (-1431), 1, (-1617));
      assertEquals((-1431), int0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      short[] shortArray0 = new short[8];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, (short) (-1), (short)450, (short)83, (short)1627);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      short[] shortArray0 = new short[2];
      long long0 = Conversion.shortArrayToLong(shortArray0, 365, 365, (-1), (-1689));
      assertEquals(365L, long0);
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      int[] intArray0 = new int[5];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, 4226, (-16), (-2881), 95);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      int[] intArray0 = new int[5];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, 7, (-3447), (-953), 14);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 7
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      int[] intArray0 = new int[2];
      long long0 = Conversion.intArrayToLong(intArray0, 2598, 0, 0, 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(10);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(9);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(4);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(2);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigitMsb0((byte)89);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: 89
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigit(102);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: 102
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)102, (-1173), "", (-3243), 100);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -3243
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      booleanArray0[5] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[5] = true;
      booleanArray0[6] = true;
      booleanArray0[7] = true;
      booleanArray0[8] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[5] = true;
      booleanArray0[6] = true;
      booleanArray0[7] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[5] = true;
      booleanArray0[6] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (-4612));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[0] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 15);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 15
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('c');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('[');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '[' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('S');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'S' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('%');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '%' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('B');
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 5, (-2576), (-2129), (byte)93);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 5
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('j');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'j' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('_');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '_' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('^');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '^' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary(']');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ']' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('Y');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Y' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('P');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'P' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('?');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '?' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('=');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '=' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('7');
      assertTrue(Arrays.equals(new boolean[] {true, true, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('6');
      assertTrue(Arrays.equals(new boolean[] {false, true, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('5');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('3');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('1');
      assertTrue(Arrays.equals(new boolean[] {true, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('B');
      boolean[] booleanArray1 = Conversion.intToBinary(1894, (-971), booleanArray0, 875, (-971));
      assertTrue(Arrays.equals(new boolean[] {true, true, false, true}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('0');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('h');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'h' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('e');
      assertEquals(7, int0);
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('c');
      assertEquals(3, int0);
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('[');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '[' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('4');
      assertEquals(2, int0);
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('?');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '?' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('3');
      assertEquals(12, int0);
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("nBools-1+dstPos is greater or equal to than 32", 1, (byte)57, (-60903), (byte)50);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'o' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('f');
      assertEquals(15, int0);
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      Conversion conversion0 = new Conversion();
  }
}
