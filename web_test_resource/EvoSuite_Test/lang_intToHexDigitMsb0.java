/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:32:58 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.Conversion;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.util.MockUUID;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Conversion_ESTest extends Conversion_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      byte[] byteArray1 = Conversion.longToByteArray((-685L), (-392), byteArray0, 0, 1);
      assertArrayEquals(new byte[] {(byte) (-1), (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      int[] intArray0 = new int[2];
      int[] intArray1 = Conversion.longToIntArray(65535, 51, intArray0, 1, 1);
      assertArrayEquals(new int[] {0, 0}, intArray1);
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      int int0 = Conversion.binaryToInt(booleanArray0, (-753), (-3406), (-3582), (-1));
      assertEquals((-3406), int0);
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      byteArray0[1] = (byte)8;
      short short0 = Conversion.byteArrayToShort(byteArray0, 1, (short) (-478), (byte)8, 1);
      assertEquals((short)2082, short0);
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byteArray0[3] = (byte)29;
      int int0 = Conversion.byteArrayToInt(byteArray0, (byte)0, (-690), (-133), 4);
      assertEquals(15204358, int0);
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      int[] intArray0 = new int[0];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, (-1), 1854L, 21, 21);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.shortToByteArray((short) (-219), (short) (-219), byteArray0, 0, (-103982));
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.shortToBinary((short) (-99), (short)0, booleanArray0, 4, (-1));
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      short[] shortArray0 = new short[4];
      long long0 = Conversion.shortArrayToLong(shortArray0, (byte)8, 0L, (short)8, 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      short[] shortArray0 = new short[6];
      long long0 = Conversion.shortArrayToLong(shortArray0, (short) (-9), (short) (-9), (-655), (short) (-9));
      assertEquals((-9L), long0);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      short[] shortArray0 = new short[2];
      int int0 = Conversion.shortArrayToInt(shortArray0, (-697), 1, 100, (byte) (-67));
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      String string0 = Conversion.longToHex(4772L, (byte)92, "", (byte)96, (-627));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      short[] shortArray0 = Conversion.intToShortArray((byte)0, 488, (short[]) null, (-1232), (-1558));
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(13);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      String string0 = Conversion.intToHex((short)0, (short) (-4536), (String) null, (-1643), (short)0);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      byte[] byteArray0 = Conversion.intToByteArray(11, 1122, (byte[]) null, 51, (-755));
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      int[] intArray0 = new int[1];
      long long0 = Conversion.intArrayToLong(intArray0, 0, 0L, 1, (-2658));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      int[] intArray0 = new int[9];
      long long0 = Conversion.intArrayToLong(intArray0, 178, (-739L), (-1807), 0);
      assertEquals((-739L), long0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      short short0 = Conversion.hexToShort("reKEC>w)R(b3*[^Lp$[", 0, (short) (-99), 98, 0);
      assertEquals((short) (-99), short0);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      long long0 = Conversion.hexToLong("nBools-1+dstPos is greater or equal to than 8", (-81), 0L, (-1), (-1));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      long long0 = Conversion.hexToLong("", (-2650), (byte) (-86), 4856, (-1282));
      assertEquals((-86L), long0);
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", (short)53, (byte)0, 6, 0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      short short0 = Conversion.byteArrayToShort(byteArray0, 14, (short) (-3081), (-5536), (-410));
      assertEquals((short) (-3081), short0);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      byte[] byteArray0 = new byte[11];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-1), 319L, 72, (byte) (-8));
      assertEquals(319L, long0);
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      short short0 = Conversion.binaryToShort(booleanArray0, (-3406), (short)0, 11, (short)0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      short short0 = Conversion.binaryToShort(booleanArray0, 56, (short) (-3081), (-5536), (-5536));
      assertEquals((short) (-3081), short0);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      long long0 = Conversion.binaryToLong(booleanArray0, (byte)0, 0L, 96, (-892));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      int int0 = Conversion.binaryToInt(booleanArray0, (-1), 93, (short) (-4), (short) (-4));
      assertEquals(93, int0);
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt((short[]) null, (-731), (-731), 137, (-1675));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      int[] intArray0 = new int[2];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray((byte) (-22), 51, intArray0, (-23), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -23
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.longToBinary((-1791), (-516), booleanArray0, (byte) (-8), 98);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong((int[]) null, 0, 0, (-25), (-25));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong("", (byte)97, 1815L, (-3248), 142);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)35, 0, (String) null, 91, (-1));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid((byte[]) null, (-1514));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort((byte[]) null, (-582), (short)81, (-582), (short)81);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, (byte) (-1), (byte)107, (-1072), 19);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong((byte[]) null, (-3358), (-1050L), 3697, 3697);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt((byte[]) null, 479, 3, 479, 479);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToShort((boolean[]) null, 57, (short) (-1341), (-918), (-918));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, 75, (short)7, (-2157), 5);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 75
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToLong((boolean[]) null, 1255, 1L, 1255, 0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToInt((boolean[]) null, 1, 2786, (-1), 2363);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null, (-1777));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToByte((boolean[]) null, (byte)8, (byte)8, (-2280), 102);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (-932));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, 1895, 0L, (-2511), 87);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1895
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[5] = true;
      booleanArray0[6] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 1671);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=0, srcPos=1671
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 82);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 82
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, (-1388));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToInt('i');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'i' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, 95);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Need at least 16 bytes for UUID
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[9];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, 3102, (-1365));
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("(nHexs-1)*4+dstPos is greater or equal to than 16");
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (-2910), 0);
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      UUID uUID0 = MockUUID.fromString("nBools-1+dstPos is greater or equal to than 8");
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, 0, 68);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBytes is greater than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)12, 0, booleanArray0, (byte)12, 5);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 12
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)50, (byte)50, booleanArray0, 371, 4417);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte)12, (byte)12, booleanArray0, (byte)12, 0);
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('8');
      boolean[] booleanArray1 = Conversion.byteToBinary((byte) (-22), (-22), booleanArray0, (-797), (-22));
      assertTrue(Arrays.equals(new boolean[] {false, false, false, true}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[23];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((byte)12, 0, booleanArray0, (byte)12, (byte)12);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 23
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      boolean[] booleanArray1 = Conversion.shortToBinary((short) (-839), 3, booleanArray0, (-1), (short) (-839));
      assertTrue(Arrays.equals(new boolean[] {false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)0, 0, booleanArray0, (-241), (short)0);
      assertTrue(Arrays.equals(new boolean[] {false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)782, 0, (boolean[]) null, 0, (short)782);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      boolean[] booleanArray1 = Conversion.intToBinary((byte)52, (byte)107, booleanArray0, 65535, (-1072));
      assertEquals(7, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((short)53, 1, booleanArray0, (byte) (-67), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -67
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.intToBinary((-1807), 0, (boolean[]) null, 2421, 0);
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[23];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((byte) (-28), 1064, booleanArray0, (byte)107, (byte)12);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      boolean[] booleanArray1 = Conversion.longToBinary(15L, (-11), booleanArray0, 348, (-1667));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToBinary((byte) (-1), (byte)0, (boolean[]) null, (byte)120, 69);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToBinary((-27L), 16, (boolean[]) null, 16, 16);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte) (-70), (-254), "", 0, 669);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte) (-114), 54, "", 0, 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)7, (-1), ",p~", (-1), (byte) (-22));
      assertEquals(",p~", string0);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      String string0 = Conversion.shortToHex((short) (-1012), (-2378), "'Y[DU0(=", 86, (-4118));
      assertEquals("'Y[DU0(=", string0);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      String string0 = Conversion.shortToHex((byte)0, 100, ",**M", 55, 0);
      assertEquals(",**M", string0);
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)33, 55, (String) null, (-124), 157);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      String string0 = Conversion.intToHex((byte) (-22), (byte) (-22), ",p~", (byte) (-22), (byte) (-22));
      assertEquals(",p~", string0);
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      String string0 = Conversion.intToHex('2', (-3248), "", (-3248), 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(3997, 2236, "", 2236, 135);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      String string0 = Conversion.longToHex((byte) (-22), 60, ",p~", (byte) (-8), (byte) (-22));
      assertEquals(",p~", string0);
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(1855L, 56, ",p~", (byte) (-22), 111);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(56, 56, ",p~", (-2108), 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -2108
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short) (-219), (short) (-219), byteArray0, (short) (-219), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -219
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      byte[] byteArray1 = Conversion.shortToByteArray((short)1055, (short) (-219), byteArray0, (short) (-219), (short) (-1));
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short) (-1), 916, byteArray0, 84, 75);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byte[] byteArray1 = Conversion.intToByteArray(406, (-177), byteArray0, (byte)12, (-177));
      assertEquals(5, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray(0, 3053, byteArray0, (byte) (-40), 1727);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray(0L, (byte) (-22), byteArray0, (byte) (-22), 7);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -22
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToByteArray(0, 57, (byte[]) null, (-877), 7);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.longToByteArray(1055L, 12, byteArray0, 96, (byte)0);
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      byte[] byteArray0 = Conversion.longToByteArray((-1479L), (-877), (byte[]) null, 7, (-877));
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray((short) (-1955), (short)602, shortArray0, (short) (-1955), 137);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      short[] shortArray0 = new short[3];
      short[] shortArray1 = Conversion.intToShortArray((-3658), 10, shortArray0, 8, (-3658));
      assertSame(shortArray0, shortArray1);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      short[] shortArray0 = new short[3];
      short[] shortArray1 = Conversion.longToShortArray((short)0, (byte)0, shortArray0, 85, (-451));
      assertArrayEquals(new short[] {(short)0, (short)0, (short)0}, shortArray1);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      short[] shortArray0 = Conversion.longToShortArray(0L, 80, (short[]) null, 53, 0);
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToShortArray((-123L), 80, (short[]) null, 80, 80);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      int[] intArray0 = new int[1];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray(95, 95, intArray0, 95, 95);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      int[] intArray0 = new int[4];
      int[] intArray1 = Conversion.longToIntArray(39L, (short)8, intArray0, (short)8, 0);
      assertSame(intArray0, intArray1);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      int[] intArray0 = new int[5];
      int[] intArray1 = Conversion.longToIntArray((short) (-4536), 157, intArray0, 0, (short) (-4536));
      assertEquals(5, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, (byte)8, (byte)1, (-1), (byte)8);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[23];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 2697, (byte)89, (-2508), (-3435));
      assertEquals((byte)89, byte0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 10, (byte) (-1), (byte) (-1), 0);
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      byte byte0 = Conversion.binaryToByte(booleanArray0, (short)0, (byte) (-1), 170, 170);
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 1575, (byte)125, 2105, (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, (-4307), (short)557, 135, 58);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      short short0 = Conversion.binaryToShort(booleanArray0, 0, (short)6784, 0, 0);
      assertEquals((short)6784, short0);
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      short short0 = Conversion.binaryToShort(booleanArray0, 615, (short)3699, (-1812), (-1428));
      assertEquals((short)3699, short0);
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[23];
      short short0 = Conversion.binaryToShort(booleanArray0, (byte)12, (short)2, (-1713), 8);
      assertEquals((short)2, short0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, (-863), (-863), (-1973), 56);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -863
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      int int0 = Conversion.binaryToInt(booleanArray0, 0, 0, 0, (short)0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 1873, (byte) (-1), (byte)44, (byte) (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      long long0 = Conversion.binaryToLong(booleanArray0, (byte)0, (byte)0, (byte)0, 6);
      assertEquals(4L, long0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, (short)0, (-1604L), 6, 55);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      long long0 = Conversion.binaryToLong(booleanArray0, (-1), (-1), (-1), (-1));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      long long0 = Conversion.binaryToLong(booleanArray0, (short)0, 55, 55, (short)0);
      assertEquals(55L, long0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('8');
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 1904, 70, 332, 70);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("1{#2:&=gq[r|qS", 102, (byte)12, 98, (-5781));
      assertEquals((byte)12, byte0);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("1{#2:&=gq[r|qS", (-1953), (byte)1, 0, (byte)1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("mu2'0HaT", 0, (byte) (-52), 0, 0);
      assertEquals((byte) (-52), byte0);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte(",p~", (byte) (-22), (byte)1, (-11313), 65535);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("", (-6255), (short)0, (-5113), 56);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("", 6, (short)1931, (short)1931, 6);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      short short0 = Conversion.hexToShort("", 0, (short)0, (-1), 0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      short short0 = Conversion.hexToShort("#sRlLHt:p1v0Yy", (-1667), (short)1931, (-11), (-11));
      assertEquals((short)1931, short0);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt((String) null, (-1), (-11313), (-92), 11);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      int int0 = Conversion.hexToInt(", srcPos=", '`', '`', '`', (-2514));
      assertEquals(96, int0);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      int int0 = Conversion.hexToInt("", 1247, (-244), (-244), 0);
      assertEquals((-244), int0);
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("", 93, (-244), 1613, 1613);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      long long0 = Conversion.hexToLong("", (-6555), 5464L, (-6555), (-2838));
      assertEquals(5464L, long0);
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong((String) null, (-6555), (-6555), 1, 21);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      long long0 = Conversion.hexToLong("", 1, 116L, 15, 0);
      assertEquals(116L, long0);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, (byte) (-108), (short)2108, 7, (byte)107);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-2508), (byte)8, (-2508), (-1));
      assertEquals((short)8, short0);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, 664, 10, (-103), 11);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 664
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      byte[] byteArray0 = new byte[10];
      int int0 = Conversion.byteArrayToInt(byteArray0, (-1), 10, (-103), (-1));
      assertEquals(10, int0);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      byte[] byteArray0 = new byte[11];
      int int0 = Conversion.byteArrayToInt(byteArray0, (byte) (-22), (-1), 10, (byte)0);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      int int0 = Conversion.byteArrayToInt(byteArray0, 0, 0, 7, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, (byte)31, (-554), 97, (byte)31);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (-2080));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2080
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, 1218, (-814L), 2861, 74);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      long long0 = Conversion.byteArrayToLong(byteArray0, (byte) (-22), (-1075L), (byte) (-22), (byte)0);
      assertEquals((-1075L), long0);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-1), 0L, (-1), (-1));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      short[] shortArray0 = new short[7];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (-1), (-1), (short) (-1), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      short[] shortArray0 = new short[3];
      int int0 = Conversion.shortArrayToInt(shortArray0, 97, (-3045), (-1485), (-9248));
      assertEquals((-3045), int0);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, 3706, 2488, (byte)8, 569);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      short[] shortArray0 = new short[3];
      int int0 = Conversion.shortArrayToInt(shortArray0, 1, 0, 2797, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      short[] shortArray0 = new short[5];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, 798, (-23), (-2555), 16);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 798
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      short[] shortArray0 = new short[5];
      long long0 = Conversion.shortArrayToLong(shortArray0, 1958, (short)1482, (short)1482, (short)0);
      assertEquals(1482L, long0);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      int[] intArray0 = new int[9];
      long long0 = Conversion.intArrayToLong(intArray0, 97, 2320L, (-361), (-1));
      assertEquals(2320L, long0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      int[] intArray0 = new int[0];
      long long0 = Conversion.intArrayToLong(intArray0, 4420, 1854L, 4420, 0);
      assertEquals(1854L, long0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      int[] intArray0 = new int[0];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, 2079, 1854L, 1, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2079
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0((byte)8);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigitMsb0((-3076));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: -3076
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigit((-581));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: -581
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      booleanArray0[5] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (-582));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[25];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=25
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[0] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('b');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('T');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'T' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('P');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'P' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('J');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'J' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('D');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('5');
      assertTrue(Arrays.equals(new boolean[] {false, true, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('/');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '/' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('#');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '#' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('e');
      assertTrue(Arrays.equals(new boolean[] {false, true, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('7');
      assertTrue(Arrays.equals(new boolean[] {true, true, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('5');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('`');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '`' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('g');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'g' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('=');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '=' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('f');
      assertEquals(15, int0);
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=2, srcPos=0
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      Conversion conversion0 = new Conversion();
  }
}
