/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:18:57 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.Conversion;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.util.MockUUID;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Conversion_ESTest extends Conversion_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte) (-1), (byte) (-1), booleanArray0, (byte) (-1), (byte) (-1));
      assertEquals(6, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)89, (-6536), "", (byte)89, 2125);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((byte)1, (-1067), "", (byte)18, 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 18
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)51, (byte)1, "Mu", 0, (byte)1);
      assertEquals("9u", string0);
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      short[] shortArray0 = new short[1];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray((byte) (-80), (-1913), shortArray0, 2066, 53);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2066
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 13, (byte)34, 1, (byte)8);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, (-1), 163, (-1), 3770);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.intToByteArray((byte)0, 821, byteArray0, 1754, (-2687));
      int int0 = Conversion.byteArrayToInt(byteArray1, (short) (-2587), (short)0, (-1), (-139));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      byteArray0[2] = (byte)1;
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, (byte)1, (byte)0, (byte)1, 7);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 7
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      long long0 = Conversion.byteArrayToLong(byteArray0, (byte)15, (-1856L), (-356), (byte) (-81));
      assertEquals((-1856L), long0);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("qv ");
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, 64, (byte) (-57));
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      short[] shortArray0 = new short[4];
      long long0 = Conversion.shortArrayToLong(shortArray0, (-1), (short) (-2793), (short) (-2793), (short) (-2793));
      assertEquals((-2793L), long0);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      short[] shortArray0 = new short[7];
      int int0 = Conversion.shortArrayToInt(shortArray0, 49, 75209, 2400, (-1810));
      assertEquals(75209, int0);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      String string0 = Conversion.longToHex(0L, 0, "", 55, (-638));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.longToBinary((-885L), 82, (boolean[]) null, 82, (-483));
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      byte[] byteArray0 = Conversion.intToByteArray((-6132), 2684, (byte[]) null, (-14), (-6132));
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      int[] intArray0 = new int[7];
      long long0 = Conversion.intArrayToLong(intArray0, 701, 0L, 3674, (-712));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      int[] intArray0 = new int[4];
      long long0 = Conversion.intArrayToLong(intArray0, (-3125), (-2412L), 11, (short)0);
      assertEquals((-2412L), long0);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      short short0 = Conversion.hexToShort("iX", 120, (short)2832, (short)2832, (-3012));
      assertEquals((short)2832, short0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      int int0 = Conversion.hexToInt("f^}ry7F37YRk", 0, 65, 0, 0);
      assertEquals(65, int0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", (byte)13, (byte) (-61), 78, (byte) (-61));
      assertEquals((byte) (-61), byte0);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      short short0 = Conversion.byteArrayToShort(byteArray0, 101, (short)353, 3399, (-2941));
      assertEquals((short)353, short0);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      short short0 = Conversion.binaryToShort(booleanArray0, (-6132), (short)0, (short)0, (short)0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      long long0 = Conversion.binaryToLong(booleanArray0, (-2967), (-2967), (-1261), (-2967));
      assertEquals((-2967L), long0);
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray((UUID) null, byteArray0, 384, (-1938));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong((short[]) null, 0, (byte)0, 1, (-3165));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt((short[]) null, 101, 101, (-666), 2006);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray((-155), (-6132), byteArray0, 6, 62);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 6
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong((int[]) null, 6, (-2), 11, 57);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      int[] intArray0 = new int[1];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, (-1), 1978L, (-2049), 52);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("", 0, (-2547), (-2547), 62);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid((byte[]) null, (short) (-16));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort((byte[]) null, (-1), (short)3699, (-1), (short)0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong((byte[]) null, 464, (-554L), 4556, 15);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt((byte[]) null, (byte)14, 64, (-6132), (-419));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToShort((boolean[]) null, (short) (-1629), (short)0, (-6132), (-1));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToLong((boolean[]) null, 71, 0L, 98, 2118);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null, (-3831));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (int) (byte) (-96));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -93
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null, 4);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (-3492));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.longToByteArray(87L, 50, byteArray0, 71, (-1));
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(1);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 1);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 4789);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=2, srcPos=4789
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      char char0 = Conversion.binaryToHexDigit(booleanArray0, (int) (short)0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, (int) (short)0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('1');
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToInt('%');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '%' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, 32);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Need at least 16 bytes for UUID
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, (byte)98, 3997);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBytes is greater than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = Conversion.uuidToByteArray(uUID0, (byte[]) null, 0, 0);
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte) (-89), (-1), booleanArray0, (byte) (-89), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -89
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte)0, (-1), booleanArray0, (byte)0, 1);
      assertTrue(Arrays.equals(new boolean[] {false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)118, (byte)118, booleanArray0, (byte)118, 48);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)1, (-1155), booleanArray0, 54, (byte)2);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 54
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short) (-2070), (-1261), booleanArray0, 64, 99);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)9671, 1, booleanArray0, (-6132), 242);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)97, (short) (-1111), booleanArray0, 11, (short)0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((-2794), (-2007), booleanArray0, 9, 826);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(88, 1300, booleanArray0, (short)0, 71);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[21];
      boolean[] booleanArray1 = Conversion.intToBinary((byte)112, 15, booleanArray0, (-1913), 0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.intToBinary(4571, (-130), booleanArray0, 1, (short) (-1));
      assertTrue(Arrays.equals(new boolean[] {}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.longToBinary(1823, 32, booleanArray0, 32, 32);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      boolean[] booleanArray1 = Conversion.longToBinary((byte)0, 7, booleanArray0, 2484, (-225));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      boolean[] booleanArray1 = Conversion.longToBinary((-4218L), 0, booleanArray0, 0, 0);
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.longToBinary(0L, 7, booleanArray0, 86, 86);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte) (-1), 62, "mm}I-:4{*'_C6JE", 62, (byte) (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)76, (byte)76, "", 3, (-6536));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      String string0 = Conversion.shortToHex((short) (-1791), (-800), "", (-232), (short) (-1791));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((byte)0, (-232), "", (byte)1, 99);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)0, (short)0, "src.length>8: src.length=", 5884, 0);
      assertEquals("src.length>8: src.length=", string0);
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex((-2056), (-837), "src.length>8: src.length=", (-837), 5);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -837
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(3159, 3159, "nBools-1+srcPos is greater or equal to than 16", (short)0, 3159);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      String string0 = Conversion.intToHex((short)1, (short)1, "src.length>8: src.length=", (short)1, (-232));
      assertEquals("src.length>8: src.length=", string0);
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(0L, 10, "SB#EEy", 10, 10);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 10
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      String string0 = Conversion.longToHex((-3597L), 0, ">,", 1, (byte) (-9));
      assertEquals(">,", string0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(1L, (-194), (String) null, 395, (byte)1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(1L, 2579, "!NV", 94, 55);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      byte[] byteArray1 = Conversion.shortToByteArray((byte)1, (byte) (-96), byteArray0, (byte)0, (byte)1);
      assertArrayEquals(new byte[] {(byte)1, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short)339, 54, (byte[]) null, 54, (short)339);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      byte[] byteArray0 = Conversion.shortToByteArray((short) (-879), (short) (-879), (byte[]) null, (short) (-879), (short) (-879));
      assertNull(byteArray0);
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      byte[] byteArray1 = Conversion.intToByteArray(0, 0, byteArray0, 0, (byte)1);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToByteArray((-1), 701, (byte[]) null, 2207, (byte)111);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byte[] byteArray1 = Conversion.intToByteArray((-1), (-1), byteArray0, (short)0, (byte)0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToByteArray(3028L, (-2303), (byte[]) null, (-1452), 10);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.intToByteArray((byte)0, 821, byteArray0, 1754, (-2687));
      // Undeclared exception!
      try { 
        Conversion.longToByteArray((byte)0, (short)0, byteArray1, (short) (-2587), 32);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byte[] byteArray1 = Conversion.longToByteArray(442L, 379, byteArray0, (byte) (-61), 0);
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      short[] shortArray0 = new short[2];
      short[] shortArray1 = Conversion.intToShortArray(7, (-1), shortArray0, 1359, (-4891));
      assertSame(shortArray0, shortArray1);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      short[] shortArray0 = new short[1];
      short[] shortArray1 = Conversion.intToShortArray(0, 0, shortArray0, (-3305), 0);
      assertArrayEquals(new short[] {(short)0}, shortArray1);
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      short[] shortArray0 = new short[2];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray(572, 3382, shortArray0, 3382, (short)15);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToShortArray((byte)1, (byte) (-14), (short[]) null, (-126), (byte)14);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      int[] intArray0 = new int[9];
      int[] intArray1 = Conversion.longToIntArray(101, 101, intArray0, 71, (-126));
      assertSame(intArray1, intArray0);
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      int[] intArray0 = new int[0];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray((-13L), 7, intArray0, (short)0, 663);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 13, (byte)91, (-3135), 1960);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 13
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 10, (byte) (-116), 0, (-1));
      assertEquals((byte) (-116), byte0);
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('3');
      byte byte0 = Conversion.binaryToByte(booleanArray0, (-1), (byte)89, (byte)89, 0);
      assertTrue(Arrays.equals(new boolean[] {false, false, true, true}, booleanArray0));
      assertEquals((byte)89, byte0);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 13, (byte)8, (byte)8, (byte)8);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, 734, (short) (-1629), (-6132), 734);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 734
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      short short0 = Conversion.binaryToShort(booleanArray0, 4, (short)294, (-97), (short) (-879));
      assertEquals((short)294, short0);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, 853, (short)32, 0, 1216);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      short short0 = Conversion.binaryToShort(booleanArray0, 734, (short) (-1629), (-6132), 0);
      assertEquals((short) (-1629), short0);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      int int0 = Conversion.binaryToInt(booleanArray0, 0, (byte)0, 1, (byte)1);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, (-327), 2296, (-757), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -327
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      int int0 = Conversion.binaryToInt(booleanArray0, (short) (-2587), 101, (short) (-2587), (-4027));
      assertEquals(101, int0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      int int0 = Conversion.binaryToInt(booleanArray0, (-1), 96, (byte)0, (byte)0);
      assertEquals(96, int0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 2174, (-820), 19, 55);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[4] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 1, (byte) (-26), (byte) (-26), (byte)13);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 5
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, (short)1, (short)1, 2602, 2602);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      long long0 = Conversion.binaryToLong(booleanArray0, 64, (short)1, 91, (byte)0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      long long0 = Conversion.binaryToLong(booleanArray0, 71, 0L, 71, (-1261));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte((String) null, (-820), (byte)92, 1305, 32);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", 0, (byte)0, 0, 0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("`EvT~qDm", (-6132), (byte)82, 99, (-419));
      assertEquals((byte)82, byte0);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      short short0 = Conversion.hexToShort("", (short) (-1629), (short) (-124), 897, (-872));
      assertEquals((short) (-124), short0);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("M/*K?>@E@V", 0, (short)198, 0, (short)198);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      short short0 = Conversion.hexToShort("}", (-1720), (short)0, (short)0, 0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("A/xLqH*{xi", 1, 1, 0, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '/' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      int int0 = Conversion.hexToInt("", (-3012), (-3012), 1823, (-3012));
      assertEquals((-3012), int0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      int int0 = Conversion.hexToInt("", (-1578), 0, 0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("", 52, 52, 436, 52);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong("", (-264), 48, 48, 2133);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      long long0 = Conversion.hexToLong("", (byte) (-9), 65535L, (byte)112, (-1913));
      assertEquals(65535L, long0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, (byte) (-2), (byte) (-2), (byte)10, 96);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      short short0 = Conversion.byteArrayToShort(byteArray0, 378, (short)0, 1404, 0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-1), (short)0, (short)0, (-1));
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-179), (short) (-649), (short) (-1365), (short) (-649));
      assertEquals((short) (-649), short0);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, 2608, (byte)0, (byte)10, 286);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      int int0 = Conversion.byteArrayToInt(byteArray0, (-3224), (byte)0, 48, (short)0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.intToByteArray((byte)0, 821, byteArray0, 1754, (-2687));
      int int0 = Conversion.byteArrayToInt(byteArray1, 104, (short) (-2587), (-1), (-139));
      assertEquals((-2587), int0);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, (byte)1, (byte)2, (-6132), 67);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      long long0 = Conversion.byteArrayToLong(byteArray0, (byte)0, (byte)0, 51, (byte)1);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, (short)0, (-445L), 353, (byte)13);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      long long0 = Conversion.byteArrayToLong(byteArray0, 16, (-1518L), 98, (byte)0);
      assertEquals((-1518L), long0);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (-659));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -659
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      short[] shortArray0 = new short[8];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short)0, (short)0, (short) (-547), (short)0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      short[] shortArray0 = new short[5];
      int int0 = Conversion.shortArrayToInt(shortArray0, (-1), (-132), (-1), (-2941));
      assertEquals((-132), int0);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      short[] shortArray0 = new short[6];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, 65, (short) (-134), (short) (-726), 7);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 65
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      short[] shortArray0 = new short[6];
      long long0 = Conversion.shortArrayToLong(shortArray0, (short)0, (short)0, 0, (short) (-1337));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      short[] shortArray0 = new short[4];
      long long0 = Conversion.shortArrayToLong(shortArray0, (-466), 1L, (-718), (byte)0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, (short) (-879), (short) (-726), (-1), 65);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      int[] intArray0 = new int[3];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, (-1), 0L, (-1), 17);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      int[] intArray0 = new int[1];
      long long0 = Conversion.intArrayToLong(intArray0, (-877), 65535L, 2836, 0);
      assertEquals(65535L, long0);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      int[] intArray0 = new int[10];
      long long0 = Conversion.intArrayToLong(intArray0, (short)1, 1978L, (-1), (short)1);
      assertEquals(1978L, long0);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigitMsb0(82);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: 82
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigit(92);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: 92
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (int) (short)1);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[4] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (int) (short)1);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 1);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 1);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[2] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 1);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 1);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, (int) (byte)1);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[21];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[21];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[21];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[21];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[21];
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[21];
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 1);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 4);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, (int) (byte)1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('p');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'p' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('G');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'G' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('=');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '=' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary(':');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ':' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('3');
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('e');
      boolean[] booleanArray1 = Conversion.shortToBinary((short) (-3555), 100, booleanArray0, 4307, (short) (-3555));
      assertTrue(Arrays.equals(new boolean[] {true, true, true, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('d');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('c');
      assertTrue(Arrays.equals(new boolean[] {false, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('R');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'R' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('O');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'O' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('N');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'N' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('G');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'G' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('>');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '>' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('3');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('1');
      assertTrue(Arrays.equals(new boolean[] {true, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('$');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '$' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('A');
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('+');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '+' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('0');
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("nBools-1+srcPos is greater or equal to than 16", 1, (short)75, (-1312), 102);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'o' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=1, srcPos=0
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      Conversion conversion0 = new Conversion();
  }
}
