/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:02:58 GMT 2025
 */

package software.amazon.event.ruler.input;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;
import software.amazon.event.ruler.MatchType;
import software.amazon.event.ruler.input.DefaultParser;
import software.amazon.event.ruler.input.InputByte;
import software.amazon.event.ruler.input.InputCharacter;
import software.amazon.event.ruler.input.SuffixEqualsIgnoreCaseParser;
import software.amazon.event.ruler.input.SuffixParser;
import software.amazon.event.ruler.input.WildcardParser;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class DefaultParser_ESTest extends DefaultParser_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      DefaultParser defaultParser0 = DefaultParser.getParser();
      MatchType matchType0 = MatchType.SUFFIX;
      // Undeclared exception!
      try { 
        defaultParser0.parse(matchType0, (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      DefaultParser defaultParser0 = DefaultParser.getParser();
      MatchType matchType0 = MatchType.SUFFIX_EQUALS_IGNORE_CASE;
      InputCharacter[] inputCharacterArray0 = defaultParser0.parse(matchType0, "");
      assertEquals(0, inputCharacterArray0.length);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      DefaultParser defaultParser0 = DefaultParser.getParser();
      MatchType matchType0 = MatchType.ANYTHING_BUT_SUFFIX;
      InputCharacter[] inputCharacterArray0 = defaultParser0.parse(matchType0, "");
      assertEquals(0, inputCharacterArray0.length);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      DefaultParser defaultParser0 = DefaultParser.getParser();
      MatchType matchType0 = MatchType.PREFIX_EQUALS_IGNORE_CASE;
      InputCharacter[] inputCharacterArray0 = defaultParser0.parse(matchType0, "software.amazon.event.ruler.input.DefaultParser");
      assertEquals(47, inputCharacterArray0.length);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      DefaultParser defaultParser0 = DefaultParser.getParser();
      MatchType matchType0 = MatchType.ANYTHING_BUT_IGNORE_CASE;
      InputCharacter[] inputCharacterArray0 = defaultParser0.parse(matchType0, ".>");
      assertEquals(2, inputCharacterArray0.length);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      DefaultParser defaultParser0 = DefaultParser.getParser();
      MatchType matchType0 = MatchType.EQUALS_IGNORE_CASE;
      InputCharacter[] inputCharacterArray0 = defaultParser0.parse(matchType0, "&Azb ztt(i+'");
      assertEquals(12, inputCharacterArray0.length);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      DefaultParser defaultParser0 = DefaultParser.getParser();
      MatchType matchType0 = MatchType.ANYTHING_BUT_WILDCARD;
      InputCharacter[] inputCharacterArray0 = defaultParser0.parse(matchType0, "r");
      assertEquals(1, inputCharacterArray0.length);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      DefaultParser defaultParser0 = DefaultParser.getParser();
      MatchType matchType0 = MatchType.EXISTS;
      InputCharacter[] inputCharacterArray0 = defaultParser0.parse(matchType0, "software.amazon.event.ruler.input.DefaultParser");
      assertEquals(47, inputCharacterArray0.length);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      DefaultParser defaultParser0 = DefaultParser.getParser();
      MatchType matchType0 = MatchType.WILDCARD;
      InputCharacter[] inputCharacterArray0 = defaultParser0.parse(matchType0, "\"9Z=;#");
      assertEquals(6, inputCharacterArray0.length);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      WildcardParser wildcardParser0 = new WildcardParser();
      SuffixEqualsIgnoreCaseParser suffixEqualsIgnoreCaseParser0 = new SuffixEqualsIgnoreCaseParser();
      SuffixParser suffixParser0 = new SuffixParser();
      DefaultParser defaultParser0 = DefaultParser.getNonSingletonParserForTesting(wildcardParser0, suffixEqualsIgnoreCaseParser0, suffixParser0, suffixEqualsIgnoreCaseParser0);
      assertNotNull(defaultParser0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      DefaultParser defaultParser0 = DefaultParser.getParser();
      InputByte inputByte0 = (InputByte)defaultParser0.parse((byte)4);
      assertEquals((byte)4, inputByte0.getByte());
  }
}
