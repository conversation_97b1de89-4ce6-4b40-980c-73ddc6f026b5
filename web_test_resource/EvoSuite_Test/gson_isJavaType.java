/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:48:05 GMT 2025
 */

package com.google.gson.internal;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import com.google.gson.ReflectionAccessFilter;
import com.google.gson.internal.ReflectionAccessFilterHelper;
import java.lang.reflect.AccessibleObject;
import java.util.LinkedList;
import java.util.List;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class ReflectionAccessFilterHelper_ESTest extends ReflectionAccessFilterHelper_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Class<ReflectionAccessFilter> class0 = ReflectionAccessFilter.class;
      boolean boolean0 = ReflectionAccessFilterHelper.isJavaType(class0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Class<ReflectionAccessFilter> class0 = ReflectionAccessFilter.class;
      boolean boolean0 = ReflectionAccessFilterHelper.isAndroidType(class0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      // Undeclared exception!
      try { 
        ReflectionAccessFilterHelper.isAnyPlatformType((Class<?>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.gson.internal.ReflectionAccessFilterHelper", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Class<Integer> class0 = Integer.class;
      // Undeclared exception!
      try { 
        ReflectionAccessFilterHelper.getFilterResult((List<ReflectionAccessFilter>) null, class0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.gson.internal.ReflectionAccessFilterHelper", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LinkedList<ReflectionAccessFilter> linkedList0 = new LinkedList<ReflectionAccessFilter>();
      ReflectionAccessFilter.FilterResult reflectionAccessFilter_FilterResult0 = ReflectionAccessFilter.FilterResult.INDECISIVE;
      ReflectionAccessFilter reflectionAccessFilter0 = mock(ReflectionAccessFilter.class, new ViolatedAssumptionAnswer());
      doReturn(reflectionAccessFilter_FilterResult0).when(reflectionAccessFilter0).check(any(java.lang.Class.class));
      linkedList0.add(reflectionAccessFilter0);
      Class<Object> class0 = Object.class;
      ReflectionAccessFilter.FilterResult reflectionAccessFilter_FilterResult1 = ReflectionAccessFilterHelper.getFilterResult(linkedList0, class0);
      assertEquals(ReflectionAccessFilter.FilterResult.ALLOW, reflectionAccessFilter_FilterResult1);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LinkedList<ReflectionAccessFilter> linkedList0 = new LinkedList<ReflectionAccessFilter>();
      ReflectionAccessFilter reflectionAccessFilter0 = mock(ReflectionAccessFilter.class, new ViolatedAssumptionAnswer());
      doReturn((ReflectionAccessFilter.FilterResult) null).when(reflectionAccessFilter0).check(any(java.lang.Class.class));
      linkedList0.add(reflectionAccessFilter0);
      Class<Object> class0 = Object.class;
      ReflectionAccessFilterHelper.getFilterResult(linkedList0, class0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Class<Object> class0 = Object.class;
      boolean boolean0 = ReflectionAccessFilterHelper.isAnyPlatformType(class0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Class<ReflectionAccessFilter> class0 = ReflectionAccessFilter.class;
      boolean boolean0 = ReflectionAccessFilterHelper.isAnyPlatformType(class0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Class<Object> class0 = Object.class;
      boolean boolean0 = ReflectionAccessFilterHelper.isJavaType(class0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Class<Integer> class0 = Integer.class;
      boolean boolean0 = ReflectionAccessFilterHelper.isAndroidType(class0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      boolean boolean0 = ReflectionAccessFilterHelper.canAccess((AccessibleObject) null, (Object) null);
      assertTrue(boolean0);
  }
}
