/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:26:25 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.File;
import java.io.StringReader;
import java.io.StringWriter;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import javax.sql.rowset.RowSetMetaDataImpl;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.DuplicateHeaderMode;
import org.apache.commons.csv.QuoteMode;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockPrintStream;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVFormat_ESTest extends CSVFormat_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withTrim(true);
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.DEFAULT.withTrim(false);
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Default;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setAutoFlush(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      assertTrue(cSVFormat1.getAutoFlush());
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(false);
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withTrailingDelimiter();
      assertTrue(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withSystemRecordSeparator();
      assertNotSame(cSVFormat0, cSVFormat1);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSystemRecordSeparator();
      assertTrue(cSVFormat1.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Default;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withSystemRecordSeparator();
      assertNotSame(cSVFormat0, cSVFormat1);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(false);
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(false);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = Character.valueOf('s');
      cSVFormat_Builder0.setCommentMarker(character0);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("");
      assertEquals("", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withAllowDuplicateHeaderNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("");
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat2.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setIgnoreHeaderCase(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("");
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('K');
      assertEquals("K", cSVFormat2.getRecordSeparator());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('?');
      assertEquals("?", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('=');
      assertEquals("=", cSVFormat2.getRecordSeparator());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      QuoteMode quoteMode0 = QuoteMode.NONE;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      QuoteMode quoteMode0 = QuoteMode.MINIMAL;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD_CSV.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      Character character0 = Character.valueOf('Z');
      CSVFormat cSVFormat1 = cSVFormat0.withQuote(character0);
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setSkipHeaderRecord(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder1.build();
      Character character0 = Character.valueOf('~');
      CSVFormat cSVFormat1 = cSVFormat0.withQuote(character0);
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAutoFlush(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder1.build();
      Character character0 = Character.valueOf('~');
      CSVFormat cSVFormat1 = cSVFormat0.withQuote(character0);
      assertTrue(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Character character0 = new Character('4');
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withTrim();
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withQuote('3');
      assertEquals('3', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('6');
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat3 = cSVFormat2.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      CSVFormat cSVFormat4 = cSVFormat3.withQuote('Q');
      assertEquals("6", cSVFormat4.getRecordSeparator());
      assertEquals('Q', (char)cSVFormat4.getQuoteCharacter());
      assertFalse(cSVFormat4.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withNullString((String) null);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withNullString("");
      assertFalse(cSVFormat1.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("38;xIs");
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("+oGex$<?t98dz0O#%j]");
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setTrim(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat1.getTrim());
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setAutoFlush(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat1.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = Character.valueOf('s');
      cSVFormat_Builder0.setCommentMarker(character0);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('V');
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertEquals('V', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase();
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(false);
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(true);
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines(true);
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines(false);
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat2.getDuplicateHeaderMode());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      Character character0 = new Character('j');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setTrim(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setSkipHeaderRecord(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder1.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAutoFlush(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder1.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
      assertTrue(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setAutoFlush(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      Object[] objectArray0 = new Object[6];
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertTrue(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      String[] stringArray0 = new String[6];
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_TSV.withHeader(stringArray0);
      assertEquals('\t', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withHeader((ResultSetMetaData) null);
      assertNotSame(cSVFormat0, cSVFormat1);
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) null);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(resultSet0);
      assertEquals("\\N", cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(resultSet0);
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrim(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder1.build();
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getTrim());
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withFirstRecordAsHeader();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setAutoFlush(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
      assertTrue(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = Character.valueOf('s');
      Character character1 = Character.valueOf('\'');
      cSVFormat_Builder0.setCommentMarker(character1);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertTrue(cSVFormat1.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setIgnoreHeaderCase(true);
      Character character0 = Character.valueOf('s');
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      Character character0 = new Character('&');
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withEscape(character0);
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withEscape('x');
      assertTrue(cSVFormat2.getAutoFlush());
      assertEquals('x', (char)cSVFormat2.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withTrim();
      CSVFormat cSVFormat2 = cSVFormat1.withEscape('=');
      assertTrue(cSVFormat2.getTrim());
      assertEquals('=', (char)cSVFormat2.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withDelimiter('=');
      assertEquals("=", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker((Character) null);
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat1.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker((Character) null);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      Character character0 = new Character('j');
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker(character0);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('x');
      assertEquals('x', (char)cSVFormat1.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setTrim(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      assertTrue(cSVFormat1.getTrim());
      assertTrue(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(false);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Default;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(true);
      assertTrue(cSVFormat1.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = Character.valueOf('s');
      cSVFormat_Builder0.setCommentMarker(character0);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames();
      assertFalse(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames();
      assertNull(cSVFormat1.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      String string0 = cSVFormat0.trim((String) null);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      String string0 = cSVFormat0.trim("");
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      String[] stringArray0 = CSVFormat.toStringArray((Object[]) null);
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      Object[] objectArray0 = new Object[4];
      String[] stringArray0 = CSVFormat.toStringArray(objectArray0);
      assertEquals(4, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      Object[] objectArray0 = new Object[0];
      String[] stringArray0 = CSVFormat.toStringArray(objectArray0);
      assertEquals(0, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      StringReader stringReader0 = new StringReader("");
      CSVParser cSVParser0 = cSVFormat0.parse(stringReader0);
      assertFalse(cSVParser0.hasHeaderComment());
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withTrim();
      boolean boolean0 = cSVFormat1.getTrim();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      boolean boolean0 = cSVFormat1.getSkipHeaderRecord();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.getIgnoreEmptyLines();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withDelimiter('K');
      char char0 = cSVFormat1.getDelimiter();
      assertEquals('K', char0);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.copy();
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Excel;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertNull(cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      CSVFormat[] cSVFormatArray0 = new CSVFormat[6];
      CSVFormat[] cSVFormatArray1 = CSVFormat.clone(cSVFormatArray0);
      assertNotSame(cSVFormatArray0, cSVFormatArray1);
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      // Undeclared exception!
      try { 
        CSVFormat.trim((CharSequence) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      String string0 = cSVFormat0.POSTGRESQL_TEXT.toString();
      assertEquals("Delimiter=<\t> Escape=<\\> QuoteMode=<ALL_NON_NULL> NullString=<\\N> RecordSeparator=<\n> SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      // Undeclared exception!
      try { 
        cSVFormat0.INFORMIX_UNLOAD_CSV.println((Appendable) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      boolean boolean0 = cSVFormat0.isQuoteCharacterSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      boolean boolean0 = cSVFormat0.isQuoteCharacterSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      boolean boolean0 = cSVFormat0.isNullStringSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      boolean boolean0 = cSVFormat0.isEscapeCharacterSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.isEscapeCharacterSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.isCommentMarkerSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      String[] stringArray0 = cSVFormat0.getHeaderComments();
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Character character0 = Character.valueOf('#');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote(character0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSet) null);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = new Character('t');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape(character0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter(".B%z(&v'_z@9DfwGL");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setNullString("Oy");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBTsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      boolean boolean0 = cSVFormat0.getAllowMissingColumnNames();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      QuoteMode quoteMode0 = cSVFormat0.getQuoteMode();
      assertEquals(QuoteMode.ALL_NON_NULL, quoteMode0);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBTsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      String[] stringArray0 = new String[1];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(stringArray0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.getSkipHeaderRecord();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAllowMissingColumnNames(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape('W');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote('*');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreEmptyLines(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      boolean boolean0 = cSVFormat0.getIgnoreHeaderCase();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreSurroundingSpaces(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrailingDelimiter(true);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      DuplicateHeaderMode duplicateHeaderMode0 = DuplicateHeaderMode.ALLOW_EMPTY;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDuplicateHeaderMode(duplicateHeaderMode0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuoteMode(quoteMode0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Object[] objectArray0 = new Object[0];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeaderComments(objectArray0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.getTrim();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBTsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator("MongoDBTsv");
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      boolean boolean0 = cSVFormat0.getIgnoreEmptyLines();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withAllowDuplicateHeaderNames(true);
      cSVFormat1.withAllowDuplicateHeaderNames();
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat1.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      QuoteMode quoteMode0 = QuoteMode.NONE;
      // Undeclared exception!
      try { 
        cSVFormat0.withQuoteMode(quoteMode0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No quotes mode set but no escape character is set
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withEscape('y');
      Character character0 = new Character('y');
      // Undeclared exception!
      try { 
        cSVFormat1.withCommentMarker(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start and the escape character cannot be the same ('y')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat(')');
      // Undeclared exception!
      try { 
        cSVFormat0.withCommentMarker(')');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the delimiter cannot be the same (')')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setTrim(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      cSVFormat0.trim("The delimiter cannot be a line break");
      assertTrue(cSVFormat0.getTrim());
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      String string0 = cSVFormat0.TDF.toString();
      assertEquals("Delimiter=<\t> QuoteChar=<\"> RecordSeparator=<\r\n> EmptyLines:ignored SurroundingSpaces:ignored SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      Object[] objectArray0 = new Object[7];
      objectArray0[0] = (Object) cSVFormat0;
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertTrue(cSVFormat1.isNullStringSet());
      assertTrue(cSVFormat1.isEscapeCharacterSet());
      assertFalse(cSVFormat1.isCommentMarkerSet());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = CSVFormat.MYSQL;
      CSVFormat cSVFormat2 = cSVFormat1.withEscape('.');
      MockPrintStream mockPrintStream0 = new MockPrintStream("f=");
      cSVFormat2.print((Object) cSVFormat0, (Appendable) mockPrintStream0, false);
      assertEquals('.', (char)cSVFormat2.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      MockFile mockFile0 = new MockFile("string", "string");
      MockPrintStream mockPrintStream0 = new MockPrintStream(mockFile0);
      Object[] objectArray0 = new Object[0];
      cSVFormat0.INFORMIX_UNLOAD_CSV.printRecord(mockPrintStream0, objectArray0);
      assertEquals(1L, mockFile0.length());
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      boolean boolean0 = cSVFormat0.isNullStringSet();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      Object[] objectArray0 = new Object[7];
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      cSVFormat1.printer();
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.getAllowDuplicateHeaderNames();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      // Undeclared exception!
      try { 
        cSVFormat0.format((Object[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      boolean boolean0 = cSVFormat0.MYSQL.equals(cSVFormat0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      Object object0 = new Object();
      boolean boolean0 = cSVFormat0.equals(object0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      boolean boolean0 = cSVFormat0.DEFAULT.equals((Object) null);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      boolean boolean0 = cSVFormat0.POSTGRESQL_TEXT.equals(cSVFormat0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withTrim();
      assertFalse(cSVFormat1.isQuoteCharacterSet());
      
      MockPrintStream mockPrintStream0 = new MockPrintStream("p2Qg");
      cSVFormat1.print((Object) cSVFormat0, (Appendable) mockPrintStream0, true);
      assertTrue(cSVFormat1.isEscapeCharacterSet());
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Object[] objectArray0 = new Object[0];
      CSVFormat cSVFormat1 = cSVFormat0.withHeaderComments(objectArray0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("uC,");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(resultSet0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      // Undeclared exception!
      try { 
        cSVFormat_Builder0.setDelimiter("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The delimiter cannot be empty
         //
         verifyException("org.apache.commons.csv.CSVFormat$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      File file0 = MockFile.createTempFile("The comment start and the escape character cannot be the same ('", (String) null);
      Charset charset0 = Charset.defaultCharset();
      CSVPrinter cSVPrinter0 = cSVFormat0.INFORMIX_UNLOAD.print(file0, charset0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('s');
      // Undeclared exception!
      try { 
        cSVFormat1.withEscape('s');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The escape character and the delimiter cannot be the same ('s')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      DuplicateHeaderMode duplicateHeaderMode0 = cSVFormat0.getDuplicateHeaderMode();
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, duplicateHeaderMode0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(false);
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      // Undeclared exception!
      try { 
        CSVFormat.valueOf("org.apache.commons.csv.CSVPrinter");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.csv.CSVFormat.Predefined.org.apache.commons.csv.CSVPrinter
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBTsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_CSV.withSkipHeaderRecord(true);
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
    Future<?> future = executor.submit(new Runnable(){ 
            @Override public void run() { 
        try {
          CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
          MockFile mockFile0 = new MockFile("=w#4IM+P%E%m77aQ");
          Path path0 = mockFile0.toPath();
          Charset charset0 = Charset.defaultCharset();
          // Undeclared exception!
          try { 
            cSVFormat0.print(path0, charset0);
            fail("Expecting exception: SecurityException");
          
          } catch(SecurityException e) {
             //
             // Security manager blocks (\"java.io.FilePermission\" \"=w#4IM+P%E%m77aQ\" \"write\")
             // java.lang.Thread.getStackTrace(Thread.java:1559)
             // org.evosuite.runtime.sandbox.MSecurityManager.checkPermission(MSecurityManager.java:424)
             // java.lang.SecurityManager.checkWrite(SecurityManager.java:979)
             // sun.nio.fs.UnixChannelFactory.open(UnixChannelFactory.java:247)
             // sun.nio.fs.UnixChannelFactory.newFileChannel(UnixChannelFactory.java:136)
             // sun.nio.fs.UnixChannelFactory.newFileChannel(UnixChannelFactory.java:148)
             // sun.nio.fs.UnixFileSystemProvider.newByteChannel(UnixFileSystemProvider.java:212)
             // java.nio.file.spi.FileSystemProvider.newOutputStream(FileSystemProvider.java:434)
             // java.nio.file.Files.newOutputStream(Files.java:216)
             // java.nio.file.Files.newBufferedWriter(Files.java:2860)
             // org.apache.commons.csv.CSVFormat.print(CSVFormat.java:1886)
             // sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
             // sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
             // sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
             // java.lang.reflect.Method.invoke(Method.java:498)
             // org.evosuite.testcase.statements.MethodStatement$1.execute(MethodStatement.java:256)
             // org.evosuite.testcase.statements.AbstractStatement.exceptionHandler(AbstractStatement.java:165)
             // org.evosuite.testcase.statements.MethodStatement.execute(MethodStatement.java:219)
             // org.evosuite.testcase.execution.TestRunnable.executeStatements(TestRunnable.java:286)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:192)
             // org.evosuite.testcase.execution.TestRunnable.call(TestRunnable.java:49)
             // java.util.concurrent.FutureTask.run(FutureTask.java:266)
             // java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
             // java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
             // java.lang.Thread.run(Thread.java:748)
             //
             verifyException("org.evosuite.runtime.sandbox.MSecurityManager", e);
          }
        } catch(Throwable t) {
            // Need to catch declared exceptions
        }
      } 
    });
    future.get(4000, TimeUnit.MILLISECONDS);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      assertFalse(cSVFormat0.isEscapeCharacterSet());
      
      Object[] objectArray0 = new Object[5];
      objectArray0[0] = (Object) cSVFormat0;
      cSVFormat0.format(objectArray0);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.MONGODB_TSV.withTrailingDelimiter(false);
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      StringWriter stringWriter0 = new StringWriter();
      StringWriter stringWriter1 = stringWriter0.append('K');
      StringBuffer stringBuffer0 = stringWriter1.getBuffer();
      CharSequence charSequence0 = CSVFormat.trim(stringBuffer0);
      assertSame(stringBuffer0, charSequence0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Character character0 = new Character('%');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote((Character) null);
      CSVFormat cSVFormat2 = cSVFormat1.copy();
      Object[] objectArray0 = new Object[3];
      objectArray0[0] = (Object) cSVFormat2;
      CSVFormat cSVFormat3 = cSVFormat2.withHeaderComments(objectArray0);
      assertFalse(cSVFormat3.isQuoteCharacterSet());
      assertTrue(cSVFormat3.isEscapeCharacterSet());
      assertFalse(cSVFormat3.isCommentMarkerSet());
      assertFalse(cSVFormat3.equals((Object)cSVFormat1));
      assertFalse(cSVFormat3.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withHeader((ResultSet) null);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('?');
      boolean boolean0 = cSVFormat0.POSTGRESQL_TEXT.equals(cSVFormat1);
      assertEquals('?', (char)cSVFormat1.getCommentMarker());
      assertFalse(cSVFormat0.equals((Object)cSVFormat1));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(class0);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_CSV.withAllowDuplicateHeaderNames();
      assertTrue(cSVFormat1.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      boolean boolean0 = cSVFormat0.getTrailingDelimiter();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      String[] stringArray0 = new String[4];
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(stringArray0);
      StringReader stringReader0 = new StringReader("org.apache.commons.csv.CSVParser");
      // Undeclared exception!
      try { 
        cSVFormat1.parse(stringReader0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // A header name is missing in [null, null, null, null]
         //
         verifyException("org.apache.commons.csv.CSVParser", e);
      }
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      char char0 = cSVFormat0.getDelimiter();
      assertEquals(',', char0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("path");
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator('*');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setSkipHeaderRecord(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder1.build();
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('~');
      assertTrue(cSVFormat1.getSkipHeaderRecord());
      assertEquals("~", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withDelimiter('s');
      assertTrue(cSVFormat2.getAutoFlush());
      assertEquals("s", cSVFormat2.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker('O');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim(false);
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(false);
      Object[] objectArray0 = new Object[0];
      CSVFormat cSVFormat3 = cSVFormat2.withHeaderComments(objectArray0);
      assertFalse(cSVFormat3.equals((Object)cSVFormat2));
      assertFalse(cSVFormat3.getTrim());
      assertTrue(cSVFormat3.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(class0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter('E');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      cSVFormat_Builder0.setIgnoreHeaderCase(true);
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames();
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }
}
