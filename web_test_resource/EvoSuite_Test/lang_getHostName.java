/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:07:21 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.File;
import org.apache.commons.lang3.JavaVersion;
import org.apache.commons.lang3.SystemUtils;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.testdata.EvoSuiteFile;
import org.evosuite.runtime.testdata.FileSystemHandling;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class SystemUtils_ESTest extends SystemUtils_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSVersionMatch("Temurin", "Temurin");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSNameMatch("j;uL]?&:.#U", "j;uL]?&:.#U");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      boolean boolean0 = SystemUtils.isJavaVersionMatch("10.5", "10.5");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      JavaVersion javaVersion0 = JavaVersion.JAVA_1_8;
      boolean boolean0 = SystemUtils.isJavaVersionAtMost(javaVersion0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      JavaVersion javaVersion0 = JavaVersion.JAVA_12;
      boolean boolean0 = SystemUtils.isJavaVersionAtLeast(javaVersion0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      EvoSuiteFile evoSuiteFile0 = new EvoSuiteFile("/Users/<USER>");
      FileSystemHandling.setPermissions(evoSuiteFile0, false, false, false);
      File file0 = SystemUtils.getUserHome();
      assertFalse(file0.canWrite());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      EvoSuiteFile evoSuiteFile0 = new EvoSuiteFile("/Users/<USER>/Desktop/java-project/commons-lang-rel-commons-lang-3.10");
      FileSystemHandling.setPermissions(evoSuiteFile0, false, false, false);
      File file0 = SystemUtils.getUserDir();
      assertEquals(0L, file0.length());
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      EvoSuiteFile evoSuiteFile0 = new EvoSuiteFile("/var/folders/sl/3mfrgmdj2n1cl2km06yxm9jh0000gn/T");
      FileSystemHandling.setPermissions(evoSuiteFile0, false, false, false);
      File file0 = SystemUtils.getJavaIoTmpDir();
      assertEquals(0L, file0.getTotalSpace());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      EvoSuiteFile evoSuiteFile0 = new EvoSuiteFile("/Library/Java/JavaVirtualMachines/temurin-8.jdk/Contents/Home/jre");
      FileSystemHandling.createFolder(evoSuiteFile0);
      File file0 = SystemUtils.getJavaHome();
      assertTrue(file0.exists());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      EvoSuiteFile evoSuiteFile0 = new EvoSuiteFile("/Library/Java/JavaVirtualMachines/temurin-8.jdk/Contents/Home/jre");
      FileSystemHandling.appendStringToFile(evoSuiteFile0, "+&7JT");
      File file0 = SystemUtils.getJavaHome();
      assertTrue(file0.canWrite());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      String string0 = SystemUtils.getEnvironmentVariable("java.vm.version", (String) null);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      String string0 = SystemUtils.getEnvironmentVariable("25.302-b08", "1.8");
      assertEquals("1.8", string0);
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      // Undeclared exception!
      try { 
        SystemUtils.isOSVersionMatch("/Library/Java/JavaVirtualMachines/temurin-8.jdk/Contents/Home/jre/lib/endorsed", (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.SystemUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      // Undeclared exception!
      try { 
        SystemUtils.isOSNameMatch("/var/folders/sl/3mfrgmdj2n1cl2km06yxm9jh0000gn/T/EvoSuite_pathingJar1135990439178324980.jar", (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      // Undeclared exception!
      try { 
        SystemUtils.isOSMatch("[Ft#Rk{ToF", ")v}", (String) null, "9");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      // Undeclared exception!
      try { 
        SystemUtils.isJavaVersionMatch("mac", (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      // Undeclared exception!
      try { 
        SystemUtils.getEnvironmentVariable((String) null, (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSVersionMatch((String) null, (String) null);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSNameMatch((String) null, "Mac OS X");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSMatch("1.3", "1.3", "1.3", "1.3");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSMatch("^@`!,/wtXqd", "1.8.0_302", "Java Virtual Machine Specification", "java.specification.version");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSMatch("1.4", (String) null, "12", "#");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSMatch("<>v:Ii3RvI[3vgv", "T4_jKmu8rC!v9gdha", "", "W %4p31");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSMatch((String) null, "<>v:Ii3RvI[3vgv", "java.vm.specification.name", "Windows 98Mac OS X");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      boolean boolean0 = SystemUtils.isJavaVersionMatch((String) null, (String) null);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      String string0 = SystemUtils.getEnvironmentVariable("JAVA_1_4", "");
      assertNotNull(string0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      String string0 = SystemUtils.getHostName();
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      SystemUtils systemUtils0 = new SystemUtils();
      assertFalse(SystemUtils.IS_OS_MAC_OSX_SNOW_LEOPARD);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      boolean boolean0 = SystemUtils.isJavaAwtHeadless();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      File file0 = SystemUtils.getUserHome();
      assertTrue(file0.canRead());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      File file0 = SystemUtils.getUserDir();
      assertEquals(0L, file0.getUsableSpace());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      File file0 = SystemUtils.getJavaHome();
      assertEquals(0L, file0.lastModified());
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      String string0 = SystemUtils.getUserName();
      assertEquals("mac", string0);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      JavaVersion javaVersion0 = JavaVersion.JAVA_1_4;
      boolean boolean0 = SystemUtils.isJavaVersionAtLeast(javaVersion0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      JavaVersion javaVersion0 = JavaVersion.JAVA_1_4;
      boolean boolean0 = SystemUtils.isJavaVersionAtMost(javaVersion0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      File file0 = SystemUtils.getJavaIoTmpDir();
      assertTrue(file0.canRead());
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      String string0 = SystemUtils.getUserName((String) null);
      assertEquals("mac", string0);
  }
}
