/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:53:46 GMT 2025
 */

package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.List;
import org.apache.commons.cli.Option;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Option_ESTest extends Option_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Option option0 = new Option("", "", false, "");
      option0.setArgs((-4454));
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot add value, list full.
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("");
      Option option0 = option_Builder0.build();
      assertEquals((-1), option0.getArgs());
      
      option0.setArgs(0);
      boolean boolean0 = option0.acceptsArg();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      option_Builder0.required(true);
      Option.Builder option_Builder1 = option_Builder0.longOpt("");
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.isRequired();
      assertEquals((-1), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      option0.addValueForProcessing("'");
      List<String> list0 = option0.getValuesList();
      assertEquals(1, list0.size());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      option0.setValueSeparator('x');
      char char0 = option0.getValueSeparator();
      assertEquals('x', char0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Option option0 = new Option("", "", false, "");
      String string0 = option0.getValue("");
      assertEquals((-1), option0.getArgs());
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.addValueForProcessing("RLEDZ?2\"+}J>##e");
      String string0 = option0.getValue();
      assertEquals("RLEDZ?2\"+}J>##e", string0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Option option0 = new Option("", "org.apache.commons.cli.OptionValidator");
      option0.setType((Object) null);
      option0.getType();
      assertEquals((-1), option0.getArgs());
      assertEquals("org.apache.commons.cli.OptionValidator", option0.getDescription());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("");
      Option option0 = option_Builder0.build();
      option0.getOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Option option0 = new Option("", "", false, "");
      option0.getOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Option option0 = new Option("", "");
      String string0 = option0.getLongOpt();
      assertEquals((-1), option0.getArgs());
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("");
      Option option0 = option_Builder0.build();
      option0.getLongOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Option option0 = new Option((String) null, false, "--");
      String string0 = option0.getKey();
      assertNull(string0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      String string0 = option0.getKey();
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, false, (String) null);
      option0.getDescription();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Option option0 = new Option((String) null, "LWIt.");
      String string0 = option0.getDescription();
      assertNotNull(string0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      option0.setArgs(834);
      int int0 = option0.getArgs();
      assertEquals(834, int0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Option option0 = new Option("", "org.apaWhe.commons.cli.Option", true, "");
      option0.setArgName("c,RNDa(SO4PpVO");
      option0.getArgName();
      assertEquals("org.apaWhe.commons.cli.Option", option0.getLongOpt());
      assertEquals("", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setArgName("");
      option0.getArgName();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.addValueForProcessing((String) null);
      try { 
        option0.getValue(2238);
        fail("Expecting exception: IndexOutOfBoundsException");
      
      } catch(IndexOutOfBoundsException e) {
         //
         // Index: 2238, Size: 1
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Option option0 = new Option("", false, "");
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      // Undeclared exception!
      try { 
        Option.builder("org.apache.commons.cli.Util");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'org.apache.commons.cli.Util' contains an illegal character : '.'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.setValueSeparator('%');
      // Undeclared exception!
      try { 
        option0.addValueForProcessing((String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("awq2?:e", false, "awq2?:e");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'awq2?:e' contains an illegal character : '?'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option(" [ARG]", " [ARG]", false, " [ARG]");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option ' [ARG]' contains an illegal character : ' '
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("[ option: ", "");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option '[ option: ' contains an illegal character : '['
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.hasValueSeparator();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("z$yuLt3p");
      option_Builder0.valueSeparator('s');
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasValueSeparator();
      assertTrue(boolean0);
      assertEquals((-1), option0.getArgs());
      assertEquals('s', option0.getValueSeparator());
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.longOpt("");
      option_Builder1.hasArgs();
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasArgs();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setArgs(2);
      boolean boolean0 = option0.hasArgs();
      assertEquals(2, option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Option option0 = new Option("", "", false, "org.apache.commons.cli.Option");
      boolean boolean0 = option0.hasArgs();
      assertEquals("", option0.getLongOpt());
      assertEquals("org.apache.commons.cli.Option", option0.getDescription());
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("z$yuLt3p");
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasArg();
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Option option0 = new Option((String) null, true, "--");
      option0.hasArg();
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Option option0 = new Option("", "");
      assertFalse(option0.hasArgs());
      
      option0.setArgs((-2));
      boolean boolean0 = option0.hasArg();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.getValue();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.addValueForProcessing("");
      String string0 = option0.getValue();
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Option option0 = new Option("", false, "");
      option0.getValueSeparator();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Option option0 = new Option("", "~.oW");
      option0.hasOptionalArg();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
      assertEquals("~.oW", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      option_Builder0.option("q");
      Option option0 = option_Builder0.build();
      option0.getKey();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Option option0 = new Option("gCg", "gCg");
      String string0 = option0.toString();
      assertEquals("[ option: gCg  :: gCg :: class java.lang.String ]", string0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Option option0 = new Option("", "org.apaWhe.commons.cli.Option", true, "");
      String string0 = option0.toString();
      assertEquals("[ option:  org.apaWhe.commons.cli.Option  [ARG] ::  :: class java.lang.String ]", string0);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Option option0 = new Option("", "");
      assertFalse(option0.hasArgs());
      
      option0.setArgs((-2));
      option0.addValueForProcessing("");
      boolean boolean0 = option0.requiresArg();
      assertEquals((-2), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      Option option0 = new Option("", "");
      assertEquals((-1), option0.getArgs());
      
      option0.setArgs((-2));
      boolean boolean0 = option0.requiresArg();
      assertEquals((-2), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.setValueSeparator('^');
      option0.addValueForProcessing("V{[&RtC(6-yQhkMQ=K}");
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("#3Z[.$l1UKP0iA(^h");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot add value, list full.
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.setValueSeparator('^');
      option0.addValueForProcessing("#3Z[.$l1UKP0iA(^h");
      assertEquals('^', option0.getValueSeparator());
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      Option option0 = new Option("", "", false, "org.apache.commons.cli.Option");
      boolean boolean0 = option0.hasLongOpt();
      assertEquals("", option0.getLongOpt());
      assertEquals("org.apache.commons.cli.Option", option0.getDescription());
      assertTrue(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      Option option0 = new Option((String) null, "");
      boolean boolean0 = option0.hasLongOpt();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      Option option0 = new Option("", "");
      assertFalse(option0.hasArgs());
      
      option0.setArgs((-2));
      option0.toString();
      assertEquals((-2), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      Option option0 = new Option("", "org.apaWhe.commons.cli.Option", true, "");
      option0.setArgName("c,RNDa(SO4PpVO");
      boolean boolean0 = option0.hasArgName();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      Option option0 = new Option("8", true, "8");
      boolean boolean0 = option0.hasArgName();
      assertFalse(boolean0);
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      String[] stringArray0 = option0.getValues();
      assertNull(stringArray0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.addValueForProcessing((String) null);
      String[] stringArray0 = option0.getValues();
      assertEquals(1, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.addValueForProcessing("' contains an illegal character : '");
      String string0 = option0.getValue((String) null);
      assertEquals("' contains an illegal character : '", string0);
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      String string0 = option0.getValue((-2));
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.addValueForProcessing((String) null);
      String string0 = option0.getValue((String) null);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      Option option0 = new Option("", "");
      Option option1 = new Option("", "");
      boolean boolean0 = option0.equals(option1);
      assertTrue(boolean0);
      assertFalse(option1.hasLongOpt());
      assertEquals((-1), option1.getArgs());
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      Option option0 = new Option("", false, "");
      Option option1 = new Option("", "", false, "");
      boolean boolean0 = option1.equals(option0);
      assertEquals((-1), option1.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      Option option0 = new Option("", "");
      Option option1 = new Option((String) null, false, (String) null);
      boolean boolean0 = option0.equals(option1);
      assertEquals((-1), option1.getArgs());
      assertFalse(option0.hasLongOpt());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      Option option0 = new Option("", "");
      boolean boolean0 = option0.equals("");
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      Option option0 = new Option("gCg", "gCg");
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("gCg");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // NO_ARGS_ALLOWED
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      Option option0 = new Option("", "{");
      option0.setOptionalArg(true);
      boolean boolean0 = option0.acceptsArg();
      assertTrue(option0.hasOptionalArg());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.optionalArg(true);
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.requiresArg();
      assertFalse(boolean0);
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.optionalArg(false);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test64()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.hasArg(false);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test65()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      // Undeclared exception!
      try { 
        option_Builder0.build();
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Either opt or longOpt must be specified
         //
         verifyException("org.apache.commons.cli.Option$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test66()  throws Throwable  {
      Option option0 = new Option("", "");
      assertFalse(option0.hasLongOpt());
      
      option0.setLongOpt("");
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test67()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("z$yuLt3p");
      Option option0 = option_Builder0.build();
      option0.getOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test68()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.getType();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test69()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      // Undeclared exception!
      try { 
        option0.addValue(",8EaJx");
        fail("Expecting exception: UnsupportedOperationException");
      
      } catch(UnsupportedOperationException e) {
         //
         // The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. 
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test70()  throws Throwable  {
      Option option0 = new Option("", "org.apache.commons.cli.Option", true, "[ option: ");
      String string0 = option0.getLongOpt();
      assertEquals("org.apache.commons.cli.Option", string0);
      assertEquals("[ option: ", option0.getDescription());
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test71()  throws Throwable  {
      Option option0 = new Option("gCg", "gCg");
      option0.hashCode();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test72()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.longOpt("");
      Option option0 = option_Builder1.build();
      option0.isRequired();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test73()  throws Throwable  {
      Option option0 = new Option("", "");
      String string0 = option0.getDescription();
      assertNotNull(string0);
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test74()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      int int0 = option0.getArgs();
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test75()  throws Throwable  {
      Option option0 = new Option("gCg", "gCg");
      option0.clearValues();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test76()  throws Throwable  {
      Option option0 = new Option("", "");
      boolean boolean0 = option0.requiresArg();
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test77()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.setDescription("");
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test78()  throws Throwable  {
      Option option0 = new Option("", "org.apache.commons.cli.Option", true, "");
      option0.setArgName("");
      boolean boolean0 = option0.hasArgName();
      assertFalse(boolean0);
      assertEquals("org.apache.commons.cli.Option", option0.getLongOpt());
      assertEquals("", option0.getDescription());
      assertTrue(option0.hasArg());
  }

  @Test(timeout = 4000)
  public void test79()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      Option option1 = (Option)option0.clone();
      assertNotSame(option1, option0);
      assertEquals(1, option1.getArgs());
  }

  @Test(timeout = 4000)
  public void test80()  throws Throwable  {
      Option option0 = new Option("8", true, "8");
      option0.getId();
      assertEquals(1, option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test81()  throws Throwable  {
      Option option0 = new Option((String) null, true, (String) null);
      option0.getValuesList();
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test82()  throws Throwable  {
      Option option0 = new Option("", "");
      Class<Object> class0 = Object.class;
      option0.setType(class0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test83()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setArgs((-2));
      option0.addValueForProcessing("");
      try { 
        option0.getValue((-2));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test84()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, false, (String) null);
      option0.setRequired(false);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.isRequired());
  }

  @Test(timeout = 4000)
  public void test85()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.getArgName();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test86()  throws Throwable  {
      Option option0 = new Option("", "");
      // Undeclared exception!
      try { 
        option0.setType((Object) "");
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.String cannot be cast to java.lang.Class
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test87()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Class<Object> class0 = Object.class;
      Option.Builder option_Builder1 = option_Builder0.type(class0);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test88()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("z$yuLt3p");
      Option.Builder option_Builder1 = option_Builder0.numberOfArgs((-1));
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test89()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.hasArg();
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test90()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.desc("");
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test91()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.required();
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test92()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder((String) null);
      Option.Builder option_Builder1 = option_Builder0.argName((String) null);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test93()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option.Builder option_Builder1 = option_Builder0.valueSeparator();
      assertSame(option_Builder0, option_Builder1);
  }
}
