/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 07:46:57 GMT 2025
 */

package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.List;
import org.apache.commons.cli.Option;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Option_ESTest extends Option_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.numberOfArgs(0);
      option_Builder0.longOpt("");
      Option option0 = option_Builder1.build();
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot add value, list full.
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.valueSeparator('/');
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.required(true);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Option option0 = new Option("", "Cannot add value, list full.", false, "");
      Object object0 = option0.getType();
      option0.setType(object0);
      assertEquals("", option0.getDescription());
      assertEquals("Cannot add value, list full.", option0.getLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      option_Builder0.required();
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.isRequired();
      assertTrue(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Option option0 = new Option((String) null, "f]|%");
      option0.setOptionalArg(true);
      boolean boolean0 = option0.hasOptionalArg();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      option0.addValueForProcessing("(fi?by+$7Crh-TJ?3l");
      List<String> list0 = option0.getValuesList();
      assertTrue(list0.contains("(fi?by+$7Crh-TJ?3l"));
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.setValueSeparator('J');
      char char0 = option0.getValueSeparator();
      assertEquals('J', char0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Option option0 = new Option("", "Siu");
      String string0 = option0.getValue(")#sq");
      assertEquals("Siu", option0.getDescription());
      assertNotNull(string0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Option option0 = new Option((String) null, "f]|%");
      String string0 = option0.getValue("");
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      Option option0 = option_Builder0.build();
      option0.getOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Option option0 = new Option("", "The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. ");
      option0.getOpt();
      assertEquals("The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. ", option0.getDescription());
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      Option option0 = option_Builder0.build();
      option0.getLongOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Option option0 = new Option((String) null, false, "\"");
      assertFalse(option0.hasLongOpt());
      
      option0.setLongOpt(",vb0=nZ@Ha.d6S");
      option0.getLongOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Option option0 = new Option("Siu", "org.apache.commons.cli.Option$Builder", true, "org.apache.commons.cli.Option$Builder");
      option0.getKey();
      assertEquals("org.apache.commons.cli.Option$Builder", option0.getLongOpt());
      assertEquals("org.apache.commons.cli.Option$Builder", option0.getDescription());
      assertTrue(option0.hasArg());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("");
      Option option0 = option_Builder0.build();
      option0.getKey();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.setLongOpt("WAl,<t5'ay|;2|w");
      int int0 = option0.getId();
      assertEquals((-1), option0.getArgs());
      assertEquals(87, int0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Option option0 = new Option("", "", true, "`Njp8f@V$'$!j=");
      String string0 = option0.getDescription();
      assertEquals("", option0.getLongOpt());
      assertEquals("`Njp8f@V$'$!j=", string0);
      assertTrue(option0.hasArg());
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Option option0 = new Option("", "");
      String string0 = option0.getDescription();
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      int int0 = option0.getArgs();
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      Option option0 = option_Builder0.build();
      option0.setArgName("j)|]N/1p");
      option0.getArgName();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Option option0 = new Option("Siu", "org.apache.commons.cli.Option$Builder", true, "org.apache.commons.cli.Option$Builder");
      boolean boolean0 = option0.acceptsArg();
      assertTrue(boolean0);
      assertEquals("org.apache.commons.cli.Option$Builder", option0.getDescription());
      assertEquals("org.apache.commons.cli.Option$Builder", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      boolean boolean0 = option0.acceptsArg();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Option option0 = new Option("Dx", true, (String) null);
      option0.addValueForProcessing("Dx");
      try { 
        option0.getValue((-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Option option0 = new Option((String) null, true, (String) null);
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      // Undeclared exception!
      try { 
        Option.builder("WAl,<t5'ay|;2|w");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'WAl,<t5'ay|;2|w' contains an illegal character : ','
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("org.apache.commons.cli.OptionValidator", true, "' contains an illegal character : '");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'org.apache.commons.cli.OptionValidator' contains an illegal character : '.'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option("W{*9SJfng(h.'@", "", true, "");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option 'W{*9SJfng(h.'@' contains an illegal character : '{'
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Option option0 = null;
      try {
        option0 = new Option(" [ARG]", "g6`?D%74TXeh*K;8*y");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The option ' [ARG]' contains an illegal character : ' '
         //
         verifyException("org.apache.commons.cli.OptionValidator", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Option option0 = new Option("", "", true, "");
      option0.hasValueSeparator();
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      option_Builder0.longOpt("\u0007");
      option_Builder0.hasArgs();
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasArgs();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Option option0 = new Option("", "");
      boolean boolean0 = option0.hasArgs();
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Option option0 = new Option((String) null, false, "\"");
      boolean boolean0 = option0.hasArg();
      assertFalse(option0.hasLongOpt());
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Option option0 = new Option("", "8f[FYTJqjsrI", true, ";?.|Nw9+");
      boolean boolean0 = option0.hasArg();
      assertTrue(boolean0);
      assertEquals("8f[FYTJqjsrI", option0.getLongOpt());
      assertEquals(";?.|Nw9+", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      option_Builder0.hasArgs();
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.hasArg();
      assertEquals((-2), option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      Option option0 = new Option("", "");
      option0.getValue();
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.hasOptionalArg();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("9j3t6");
      Option.Builder option_Builder1 = option_Builder0.option("9j3t6");
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      Option option0 = option_Builder0.build();
      String string0 = option0.toString();
      assertEquals((-1), option0.getArgs());
      assertEquals("[ option: Dx  :: null :: class java.lang.String ]", string0);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Option option0 = new Option("", "", true, "`Njp8f@V$'$!j=");
      String string0 = option0.toString();
      assertEquals("[ option:    [ARG] :: `Njp8f@V$'$!j= :: class java.lang.String ]", string0);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      option_Builder0.hasArgs();
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.requiresArg();
      assertTrue(boolean0);
      assertEquals((-2), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("9j3t6");
      option_Builder0.optionalArg(true);
      Option option0 = option_Builder0.build();
      boolean boolean0 = option0.requiresArg();
      assertEquals(1, option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.valueSeparator();
      option_Builder1.numberOfArgs(1);
      option_Builder0.longOpt("");
      Option option0 = option_Builder1.build();
      option0.addValueForProcessing("");
      assertEquals(1, option0.getArgs());
      assertEquals('=', option0.getValueSeparator());
      assertTrue(option0.hasValueSeparator());
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      Option option0 = new Option((String) null, false, "\"");
      assertFalse(option0.hasLongOpt());
      
      option0.setLongOpt(",vb0=nZ@Ha.d6S");
      boolean boolean0 = option0.hasLongOpt();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      boolean boolean0 = option0.hasLongOpt();
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      option_Builder0.hasArgs();
      Option option0 = option_Builder0.build();
      String string0 = option0.toString();
      assertEquals("[ option: Dx [ARG...] :: null :: class java.lang.String ]", string0);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.numberOfArgs(2997);
      Option.Builder option_Builder2 = option_Builder1.longOpt("=WMl%*uE{");
      Option option0 = option_Builder2.build();
      boolean boolean0 = option0.hasArgs();
      assertEquals(2997, option0.getArgs());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      Option option0 = option_Builder0.build();
      option0.setArgName("j)|]N/1p");
      boolean boolean0 = option0.hasArgName();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      boolean boolean0 = option0.hasArgName();
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      String[] stringArray0 = option0.getValues();
      assertEquals((-1), option0.getArgs());
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      option0.setArgs(3143);
      option0.addValueForProcessing((String) null);
      option0.getValues();
      assertEquals(3143, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, true, (String) null);
      option0.getValue((-211));
      assertTrue(option0.hasArg());
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.setArgs(1);
      option0.addValueForProcessing((String) null);
      try { 
        option0.getValue(367);
        fail("Expecting exception: IndexOutOfBoundsException");
      
      } catch(IndexOutOfBoundsException e) {
         //
         // Index: 367, Size: 1
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getValue((String) null);
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      option0.getKey();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      Option option0 = new Option("", "org.apache.commons.cli.Util");
      Option option1 = new Option("", "org.apache.commons.cli.Util");
      boolean boolean0 = option0.equals(option1);
      assertEquals("org.apache.commons.cli.Util", option1.getDescription());
      assertTrue(boolean0);
      assertEquals((-1), option1.getArgs());
      assertFalse(option1.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.longOpt("L}88u[aR,");
      Option option1 = option_Builder1.build();
      boolean boolean0 = option0.equals(option1);
      assertFalse(boolean0);
      assertEquals((-1), option0.getArgs());
      assertEquals((-1), option1.getArgs());
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      Option option1 = new Option("", true, (String) null);
      boolean boolean0 = option0.equals(option1);
      assertFalse(boolean0);
      assertFalse(option1.hasLongOpt());
      assertTrue(option1.hasArg());
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      Option option0 = new Option("", "");
      boolean boolean0 = option0.equals(option0);
      assertTrue(boolean0);
      assertEquals((-1), option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      // Undeclared exception!
      try { 
        option0.addValueForProcessing((String) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // NO_ARGS_ALLOWED
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.optionalArg(false);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.hasArg(false);
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      // Undeclared exception!
      try { 
        option_Builder0.build();
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Either opt or longOpt must be specified
         //
         verifyException("org.apache.commons.cli.Option$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.getOpt();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test64()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      Object object0 = option0.getType();
      boolean boolean0 = option0.equals(object0);
      assertEquals((-1), option0.getArgs());
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test65()  throws Throwable  {
      Option option0 = new Option("", true, "");
      // Undeclared exception!
      try { 
        option0.addValue("");
        fail("Expecting exception: UnsupportedOperationException");
      
      } catch(UnsupportedOperationException e) {
         //
         // The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. 
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test66()  throws Throwable  {
      Option option0 = new Option("", "", false, "H8^gv_PY|*g!GCAX");
      String string0 = option0.getLongOpt();
      assertEquals("H8^gv_PY|*g!GCAX", option0.getDescription());
      assertEquals((-1), option0.getArgs());
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test67()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      option0.hashCode();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test68()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null);
      option0.isRequired();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test69()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      Option option0 = option_Builder0.build();
      option0.getDescription();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test70()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      Option option0 = option_Builder0.build();
      int int0 = option0.getArgs();
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test71()  throws Throwable  {
      Option option0 = new Option("", "J", true, "");
      option0.clearValues();
      assertEquals("", option0.getDescription());
      assertTrue(option0.hasArg());
      assertEquals("J", option0.getLongOpt());
  }

  @Test(timeout = 4000)
  public void test72()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      Option option0 = option_Builder0.build();
      option0.getValueSeparator();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test73()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.numberOfArgs((-1150));
      option_Builder1.longOpt("");
      Option option0 = option_Builder1.build();
      // Undeclared exception!
      try { 
        option0.addValueForProcessing("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot add value, list full.
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test74()  throws Throwable  {
      Option option0 = new Option("Siu", "org.apache.commons.cli.Option$Builder", true, "org.apache.commons.cli.Option$Builder");
      assertEquals("org.apache.commons.cli.Option$Builder", option0.getDescription());
      
      option0.setDescription("Bj*Wn?+.hG]ba:JZ+");
      assertTrue(option0.hasArg());
  }

  @Test(timeout = 4000)
  public void test75()  throws Throwable  {
      Option option0 = new Option("", true, "");
      option0.setArgName("");
      boolean boolean0 = option0.hasArgName();
      assertFalse(boolean0);
      assertFalse(option0.hasLongOpt());
      assertEquals(1, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test76()  throws Throwable  {
      Option option0 = new Option("", true, "'");
      Option option1 = (Option)option0.clone();
      assertEquals("'", option1.getDescription());
      assertNotSame(option1, option0);
      assertFalse(option1.hasLongOpt());
      assertTrue(option1.hasArg());
  }

  @Test(timeout = 4000)
  public void test77()  throws Throwable  {
      Option option0 = new Option("", true, "'");
      // Undeclared exception!
      try { 
        option0.getId();
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test78()  throws Throwable  {
      Option option0 = new Option((String) null, (String) null, false, (String) null);
      option0.getValuesList();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test79()  throws Throwable  {
      Option option0 = new Option("", true, "");
      Class<Object> class0 = Object.class;
      option0.setType(class0);
      assertEquals(1, option0.getArgs());
      assertFalse(option0.hasLongOpt());
  }

  @Test(timeout = 4000)
  public void test80()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      option0.setArgs(3143);
      option0.addValueForProcessing((String) null);
      option0.getValue();
      assertEquals(3143, option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test81()  throws Throwable  {
      Option option0 = new Option("", "Siu");
      option0.setRequired(false);
      assertFalse(option0.isRequired());
      assertFalse(option0.hasLongOpt());
      assertEquals((-1), option0.getArgs());
      assertEquals("", option0.getOpt());
      assertEquals("Siu", option0.getDescription());
  }

  @Test(timeout = 4000)
  public void test82()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      Option option0 = option_Builder0.build();
      option0.getArgName();
      assertEquals((-1), option0.getArgs());
  }

  @Test(timeout = 4000)
  public void test83()  throws Throwable  {
      Option option0 = new Option((String) null, false, (String) null);
      // Undeclared exception!
      try { 
        option0.setType((Object) "");
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.String cannot be cast to java.lang.Class
         //
         verifyException("org.apache.commons.cli.Option", e);
      }
  }

  @Test(timeout = 4000)
  public void test84()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("9j3t6");
      Class<Object> class0 = Object.class;
      Option.Builder option_Builder1 = option_Builder0.type(class0);
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test85()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      option_Builder0.hasArgs();
      Option option0 = option_Builder0.build();
      option0.addValueForProcessing(")SrO8aV?zp;E0f)9~)");
      boolean boolean0 = option0.requiresArg();
      assertFalse(boolean0);
      assertFalse(option0.hasValueSeparator());
      assertEquals((-2), option0.getArgs());
      assertTrue(option0.hasArg());
  }

  @Test(timeout = 4000)
  public void test86()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder("Dx");
      option_Builder0.hasArg();
      Option option0 = option_Builder0.build();
      assertTrue(option0.hasArg());
      
      option0.addValueForProcessing("[ option: Dx  [ARG] :: null :: class java.lang.String ]");
      boolean boolean0 = option0.requiresArg();
      assertFalse(boolean0);
      assertFalse(option0.hasValueSeparator());
  }

  @Test(timeout = 4000)
  public void test87()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.desc("' contains an illegal character : '");
      assertSame(option_Builder0, option_Builder1);
  }

  @Test(timeout = 4000)
  public void test88()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.argName("");
      assertSame(option_Builder1, option_Builder0);
  }

  @Test(timeout = 4000)
  public void test89()  throws Throwable  {
      Option.Builder option_Builder0 = Option.builder();
      Option.Builder option_Builder1 = option_Builder0.valueSeparator();
      option_Builder0.longOpt("");
      Option option0 = option_Builder1.build();
      boolean boolean0 = option0.hasValueSeparator();
      assertEquals('=', option0.getValueSeparator());
      assertEquals((-1), option0.getArgs());
      assertTrue(boolean0);
  }
}
