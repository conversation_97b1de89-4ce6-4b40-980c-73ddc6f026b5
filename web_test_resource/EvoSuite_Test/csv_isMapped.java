/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:27:00 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.PipedReader;
import java.io.PipedWriter;
import java.io.Reader;
import java.io.StringReader;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.List;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVRecord_ESTest extends CSVRecord_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      StringReader stringReader0 = new StringReader("No header mapping was specified, the record values can't be accessed by name");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 138L, (-333L));
      String[] stringArray0 = new String[8];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "No header mapping was specified, the record values can't be accessed by name", 7312L, 7312L);
      boolean boolean0 = cSVRecord0.isSet(8);
      assertEquals(8, cSVRecord0.size());
      assertEquals(7312L, cSVRecord0.getRecordNumber());
      assertFalse(boolean0);
      assertEquals(7312L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "9f<R[)%>ct5S", 0L, 0L);
      String[] stringArray1 = cSVRecord0.values();
      assertSame(stringArray1, stringArray0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      PipedWriter pipedWriter0 = new PipedWriter();
      PipedReader pipedReader0 = new PipedReader(pipedWriter0);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(pipedReader0, cSVFormat0, 0L, (-1692L));
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "org.apache.commons.csv.QuoteMode", (-1692L), 0L);
      HashMap<String, String> hashMap0 = new HashMap<String, String>();
      hashMap0.put("MINIMAL", "org.apache.commons.csv.CSVRecord");
      cSVRecord0.putIn(hashMap0);
      assertEquals((-1692L), cSVRecord0.getRecordNumber());
      assertEquals(2, cSVRecord0.size());
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      PipedWriter pipedWriter0 = new PipedWriter();
      PipedReader pipedReader0 = new PipedReader(pipedWriter0);
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = CSVParser.parse((Reader) pipedReader0, cSVFormat0);
      String[] stringArray0 = new String[8];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "org.apache.commons.csv.Token", 87L, 87L);
      long long0 = cSVRecord0.getRecordNumber();
      assertEquals(8, cSVRecord0.size());
      assertEquals(87L, cSVRecord0.getCharacterPosition());
      assertEquals(87L, long0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      ByteArrayInputStream byteArrayInputStream0 = new ByteArrayInputStream(byteArray0);
      Charset charset0 = Charset.defaultCharset();
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVParser cSVParser0 = CSVParser.parse((InputStream) byteArrayInputStream0, charset0, cSVFormat0);
      String[] stringArray0 = new String[4];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", (byte)7, 4249L);
      cSVRecord0.getParser();
      assertEquals(4, cSVRecord0.size());
      assertEquals(7L, cSVRecord0.getRecordNumber());
      assertEquals(4249L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      String[] stringArray0 = new String[9];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "Mapping for %s not found, expected one of %s", 1629L, 0L);
      long long0 = cSVRecord0.getCharacterPosition();
      assertEquals(1629L, cSVRecord0.getRecordNumber());
      assertEquals(9, cSVRecord0.size());
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      MockFile mockFile0 = new MockFile((String) null, "");
      Path path0 = mockFile0.toPath();
      Charset charset0 = Charset.defaultCharset();
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVParser cSVParser0 = CSVParser.parse(path0, charset0, cSVFormat0);
      String[] stringArray0 = new String[0];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", 2139L, 2139L);
      long long0 = cSVRecord0.getCharacterPosition();
      assertEquals(2139L, cSVRecord0.getRecordNumber());
      assertEquals(2139L, long0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      ByteArrayInputStream byteArrayInputStream0 = new ByteArrayInputStream(byteArray0);
      Charset charset0 = Charset.defaultCharset();
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVParser cSVParser0 = CSVParser.parse((InputStream) byteArrayInputStream0, charset0, cSVFormat0);
      String[] stringArray0 = new String[4];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "hM<W$7bu^;", 0L, (byte) (-13));
      HashMap<String, String> hashMap0 = new HashMap<String, String>();
      cSVRecord0.putIn(hashMap0);
      assertEquals((-13L), cSVRecord0.getCharacterPosition());
      assertEquals(0L, cSVRecord0.getRecordNumber());
      assertEquals(4, cSVRecord0.size());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      StringReader stringReader0 = new StringReader("|!?}]z)Y");
      CSVFormat cSVFormat0 = CSVFormat.newFormat('@');
      CSVParser cSVParser0 = CSVParser.parse((Reader) stringReader0, cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      cSVRecord0.isMapped("|!?}]z)Y");
      assertEquals(1, cSVRecord0.size());
      assertEquals(0L, cSVRecord0.getCharacterPosition());
      assertEquals(1L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      StringReader stringReader0 = new StringReader("No header mapping was specified, the record values can't be accessed by name");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 138L, (-333L));
      String[] stringArray0 = new String[8];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "No header mapping was specified, the record values can't be accessed by name", 7312L, 7312L);
      cSVRecord0.stream();
      assertEquals(7312L, cSVRecord0.getRecordNumber());
      assertEquals(8, cSVRecord0.size());
      assertEquals(7312L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      StringReader stringReader0 = new StringReader("No header mapping was specified, the record values can't be accessed by name");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 138L, (-333L));
      String[] stringArray0 = new String[8];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "No header mapping was specified, the record values can't be accessed by name", 7312L, 7312L);
      List<String> list0 = cSVRecord0.toList();
      assertEquals(7312L, cSVRecord0.getCharacterPosition());
      assertFalse(list0.isEmpty());
      assertEquals(7312L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVParser cSVParser0 = CSVParser.parse("No header mapping was specified, the record values can't be accessed by name", cSVFormat0);
      String[] stringArray0 = new String[7];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", 1L, 0L);
      boolean boolean0 = cSVRecord0.isSet(0);
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertTrue(boolean0);
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      StringReader stringReader0 = new StringReader("|!?}]z)Y");
      CSVFormat cSVFormat0 = CSVFormat.newFormat('@');
      CSVParser cSVParser0 = CSVParser.parse((Reader) stringReader0, cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      boolean boolean0 = cSVRecord0.isSet(295);
      assertEquals(0L, cSVRecord0.getCharacterPosition());
      assertFalse(boolean0);
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertEquals(1, cSVRecord0.size());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      StringReader stringReader0 = new StringReader("No header mapping was specified, the record values can't be accessed by name");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 138L, (-333L));
      String[] stringArray0 = new String[8];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "No header mapping was specified, the record values can't be accessed by name", 7312L, 7312L);
      boolean boolean0 = cSVRecord0.isConsistent();
      assertEquals(8, cSVRecord0.size());
      assertTrue(boolean0);
      assertEquals(7312L, cSVRecord0.getCharacterPosition());
      assertEquals(7312L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      String[] stringArray0 = new String[9];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "Mapping for %s not found, expected one of %s", 1629L, 0L);
      boolean boolean0 = cSVRecord0.isSet(", values=");
      assertEquals(1629L, cSVRecord0.getRecordNumber());
      assertEquals(9, cSVRecord0.size());
      assertFalse(boolean0);
      assertEquals(0L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVParser cSVParser0 = CSVParser.parse("%'w3P*ufi", cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      // Undeclared exception!
      try { 
        cSVRecord0.get("[R&#v");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // No header mapping was specified, the record values can't be accessed by name
         //
         verifyException("org.apache.commons.csv.CSVRecord", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      StringReader stringReader0 = new StringReader("No header mapping was specified, the record values can't be accessed by name");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 138L, (-333L));
      String[] stringArray0 = new String[8];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "No header mapping was specified, the record values can't be accessed by name", 7312L, 7312L);
      int int0 = cSVRecord0.size();
      assertEquals(7312L, cSVRecord0.getCharacterPosition());
      assertEquals(7312L, cSVRecord0.getRecordNumber());
      assertEquals(8, int0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      String[] stringArray0 = new String[9];
      PipedReader pipedReader0 = new PipedReader(1058);
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVParser cSVParser0 = CSVParser.parse((Reader) pipedReader0, cSVFormat0);
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "org.apache.commons.csv.Token", 0L, 159L);
      long long0 = cSVRecord0.getRecordNumber();
      assertEquals(9, cSVRecord0.size());
      assertEquals(159L, cSVRecord0.getCharacterPosition());
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      StringReader stringReader0 = new StringReader("The quoteChar cannot be a line break");
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 1L, 1L);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      cSVRecord0.getParser();
      assertEquals(1, cSVRecord0.size());
      assertEquals(1L, cSVRecord0.getCharacterPosition());
      assertEquals(1L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      StringReader stringReader0 = new StringReader("No header mapping was specified, the record values can't be accessed by name");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 138L, (-333L));
      String[] stringArray0 = new String[8];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "No header mapping was specified, the record values can't be accessed by name", 7312L, 7312L);
      String[] stringArray1 = cSVRecord0.values();
      assertEquals(7312L, cSVRecord0.getCharacterPosition());
      assertEquals(8, stringArray1.length);
      assertEquals(7312L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      StringReader stringReader0 = new StringReader("No header mapping was specified, the record values can't be accessed by name");
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVParser cSVParser0 = new CSVParser(stringReader0, cSVFormat0, 138L, (-333L));
      String[] stringArray0 = new String[8];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "No header mapping was specified, the record values can't be accessed by name", 7312L, 7312L);
      String string0 = cSVRecord0.toString();
      assertEquals(7312L, cSVRecord0.getCharacterPosition());
      assertEquals("CSVRecord [comment='No header mapping was specified, the record values can't be accessed by name', recordNumber=7312, values=[null, null, null, null, null, null, null, null]]", string0);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      StringReader stringReader0 = new StringReader("|!?}]z)Y");
      CSVFormat cSVFormat0 = CSVFormat.newFormat('@');
      CSVParser cSVParser0 = CSVParser.parse((Reader) stringReader0, cSVFormat0);
      CSVRecord cSVRecord0 = cSVParser0.nextRecord();
      cSVRecord0.toMap();
      assertEquals(1L, cSVRecord0.getRecordNumber());
      assertEquals(0L, cSVRecord0.getCharacterPosition());
      assertEquals(1, cSVRecord0.size());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      ByteArrayInputStream byteArrayInputStream0 = new ByteArrayInputStream(byteArray0);
      Charset charset0 = Charset.defaultCharset();
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVParser cSVParser0 = CSVParser.parse((InputStream) byteArrayInputStream0, charset0, cSVFormat0);
      String[] stringArray0 = new String[4];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "hM<W$7bu^;", 0L, (byte) (-13));
      long long0 = cSVRecord0.getCharacterPosition();
      assertEquals(0L, cSVRecord0.getRecordNumber());
      assertEquals(4, cSVRecord0.size());
      assertEquals((-13L), long0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVParser cSVParser0 = CSVParser.parse("]", cSVFormat0);
      String[] stringArray0 = new String[9];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "]", 861L, 861L);
      cSVRecord0.getComment();
      assertEquals(9, cSVRecord0.size());
      assertEquals(861L, cSVRecord0.getCharacterPosition());
      assertEquals(861L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      String[] stringArray0 = new String[9];
      PipedReader pipedReader0 = new PipedReader(1058);
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVParser cSVParser0 = CSVParser.parse((Reader) pipedReader0, cSVFormat0);
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "org.apache.commons.csv.Token", 0L, 159L);
      cSVRecord0.iterator();
      assertEquals(9, cSVRecord0.size());
      assertEquals(0L, cSVRecord0.getRecordNumber());
      assertEquals(159L, cSVRecord0.getCharacterPosition());
  }
}
