/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:30:15 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.Conversion;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.util.MockUUID;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Conversion_ESTest extends Conversion_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      boolean[] booleanArray1 = Conversion.intToBinary(1, 0, booleanArray0, 1470, (-1319));
      assertEquals(8, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(0, 69, booleanArray0, (-131), (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)0, 8, "(nBytes-1)*8+dstPos is greater or equal to than 32", 2874, 2874);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[3] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 0, 0, 0, (short)14);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      int[] intArray0 = new int[3];
      intArray0[1] = 1;
      long long0 = Conversion.intArrayToLong(intArray0, 1, 1232L, 1, 1);
      assertEquals(2L, long0);
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)0, (short)0, booleanArray0, (short)0, 0);
      assertEquals(7, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      short short0 = Conversion.hexToShort("}WC07ZhqMO", (byte)97, (byte)100, (short) (-1851), (-1422));
      assertEquals((short)100, short0);
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      long long0 = Conversion.hexToLong("FO$]}_o", (-349), (-2L), (-2825), (-349));
      assertEquals((-2L), long0);
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      int int0 = Conversion.hexToInt("", 199, 199, (-406), (-406));
      assertEquals(199, int0);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte) (-99), (-499), booleanArray0, 0, (-161));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-4545), (byte)0, (byte)0, 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      long long0 = Conversion.binaryToLong(booleanArray0, 0, 0L, 0, 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 74, (byte) (-102), (short) (-1), (short) (-1));
      assertEquals((byte) (-102), byte0);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToIntArray((-3822L), (-1319), (int[]) null, (-1319), 1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("", (short) (-1851), (short) (-1149), (short) (-1149), 3);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte) (-108), (-4174), booleanArray0, 0, 95);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid((byte[]) null, (short) (-1));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort((byte[]) null, (-30), (short)100, (-2035), (-30));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToLong((boolean[]) null, 472, 0L, 90, 472);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null, 0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null, 102);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit((boolean[]) null, 1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, 52, (byte)97, (-2164), (byte)100);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 52
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 65);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('1');
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (byte)0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Need at least 16 bytes for UUID
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, (byte) (-79), 86);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBytes is greater than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[0];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (-3618), 0);
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)102, 1, (boolean[]) null, (byte) (-97), (byte)2);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)91, 1, booleanArray0, 59, 59);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.byteToBinary((byte) (-15), 107, (boolean[]) null, (-1319), (-630));
      assertNull(booleanArray0);
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)1483, (-3734), booleanArray0, (-1), (short)1483);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)81, (-1161), booleanArray0, 2800, 237);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2800
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)102, (short)102, booleanArray0, 32, (short)102);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.shortToBinary((short) (-421), (-1255), booleanArray0, (-1), 0);
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)90, (-407), booleanArray0, (-1302), (-1));
      assertTrue(Arrays.equals(new boolean[] {}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      boolean[] booleanArray1 = Conversion.intToBinary(2829, 0, booleanArray0, 0, 1);
      assertTrue(Arrays.equals(new boolean[] {true, false, false, false, false, false, false, false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((-1274), (-58), booleanArray0, (byte)100, 56);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 100
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      boolean[] booleanArray1 = Conversion.intToBinary(0, 0, booleanArray0, 48, 0);
      assertTrue(Arrays.equals(new boolean[] {false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      boolean[] booleanArray1 = Conversion.longToBinary(4294967295L, 0, booleanArray0, (-1), (byte) (-102));
      assertEquals(4, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.longToBinary(1332, 7, booleanArray0, 7, 57);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      boolean[] booleanArray1 = Conversion.longToBinary(0L, (-4174), booleanArray0, 10, 0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.longToBinary((-1461L), 75, booleanArray0, 75, 75);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)71, (byte)71, "", (byte)71, (byte)71);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)78, (byte)78, (String) null, (byte)78, 0);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)97, (byte)97, "", (-435), (byte) (-45));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)1, 1, "(nBytes-1)*8+srcPos is greater or equal to than 16", 0, 0);
      assertEquals("(nBytes-1)*8+srcPos is greater or equal to than 16", string0);
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)2532, (short)2532, "", (-3431), (-336));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      String string0 = Conversion.intToHex((byte)88, (byte)97, "%?oX}]IB{i", (-853), (-915));
      assertEquals("%?oX}]IB{i", string0);
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(4, 480, (String) null, 0, 4);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(0, (-3883), ")in0gE#8=phG_", (-3883), 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -3883
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      String string0 = Conversion.longToHex((-3822L), 0, "OG5i?[f}}h9", 0, (-1391));
      assertEquals("OG5i?[f}}h9", string0);
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(0L, 15, "OG5i[f}}h9", (-4367), 2756);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      String string0 = Conversion.longToHex((-277L), (byte)0, "", (byte)0, 0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short)1, 1, byteArray0, 1, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byte[] byteArray1 = Conversion.shortToByteArray((short)77, 1, byteArray0, 73, (-1349));
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.shortToByteArray((short) (-1), (byte)0, byteArray0, 101, (byte)0);
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      byte[] byteArray0 = new byte[4];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short)1, 2837, byteArray0, 0, 1117);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray((-458), (-458), byteArray0, (-2693), (byte)5);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2693
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToByteArray(1, (short)2532, (byte[]) null, 1, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.intToByteArray(0, (byte)0, byteArray0, (-2413), (byte)0);
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      byte[] byteArray1 = Conversion.intToByteArray(87, 1, byteArray0, 1571, (-1988));
      assertEquals(6, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("Cg!+7+58");
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, (byte[]) null, 1, 1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[8];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (byte)2, (byte) (-97));
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray1);
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byte[] byteArray1 = Conversion.longToByteArray((-13L), 1969, byteArray0, (byte)88, 0);
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToByteArray(945L, 79, (byte[]) null, 79, 79);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      short[] shortArray0 = new short[1];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray(88, 4035, shortArray0, 55, 88);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      short[] shortArray0 = new short[1];
      short[] shortArray1 = Conversion.intToShortArray(0, (-1397), shortArray0, (short)0, (short)0);
      assertSame(shortArray1, shortArray0);
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      short[] shortArray0 = new short[1];
      short[] shortArray1 = Conversion.intToShortArray(0, 50, shortArray0, 0, (-1));
      assertSame(shortArray1, shortArray0);
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToShortArray(0L, 3949, (short[]) null, 0, 1356);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      short[] shortArray0 = new short[9];
      short[] shortArray1 = Conversion.longToShortArray((byte)0, 12, shortArray0, 366, (byte)0);
      assertArrayEquals(new short[] {(short)0, (short)0, (short)0, (short)0, (short)0, (short)0, (short)0, (short)0, (short)0}, shortArray1);
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      short[] shortArray0 = Conversion.longToShortArray((-1788L), (-5152), (short[]) null, (-5152), (-5152));
      assertNull(shortArray0);
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      int[] intArray0 = Conversion.longToIntArray(0L, 1332, (int[]) null, (-2215), (-669));
      assertNull(intArray0);
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToIntArray((-86), (-86), (int[]) null, 63, 16);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      int[] intArray0 = new int[5];
      int[] intArray1 = Conversion.longToIntArray(0L, 0, intArray0, 0, 0);
      assertSame(intArray1, intArray0);
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      int[] intArray0 = new int[6];
      // Undeclared exception!
      try { 
        Conversion.longToIntArray(0, 7, intArray0, 91, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 91
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      byte byte0 = Conversion.binaryToByte(booleanArray0, 0, (byte)16, (byte)16, 0);
      assertEquals((byte)16, byte0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 79, (byte) (-102), 74, 79);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, (-1), (-2038), 94, 86);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      long long0 = Conversion.binaryToLong(booleanArray0, (short)0, 1949L, 1, (short)1);
      assertEquals(1951L, long0);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, (-692), 0L, (-203), (byte)100);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -692
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      long long0 = Conversion.binaryToLong(booleanArray0, (-3608), (-3608), 0, 0);
      assertEquals((-3608L), long0);
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToLong(booleanArray0, 13, 13, 438, 13);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      long long0 = Conversion.binaryToLong(booleanArray0, 3427, 3427, (-15), (-15));
      assertEquals(3427L, long0);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("HEh7`<E-^:}\f#", 66, (byte)84, 66, (byte)84);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", 0, (byte) (-82), 0, 0);
      assertEquals((byte) (-82), byte0);
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("", (-1), (byte) (-94), (-1), (-1));
      assertEquals((byte) (-94), byte0);
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("", 2407, (short)54, (short)54, 1158);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      short short0 = Conversion.hexToShort("M*%CD5e)\"g27S9", 0, (short) (-1851), (short) (-1851), 0);
      assertEquals((short) (-1851), short0);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt((String) null, (-1062), 82, 82, 57);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      int int0 = Conversion.hexToInt("nBools-1+srcPos is greater or equal to than 64", 88, (-2338), (short)0, (-2338));
      assertEquals((-2338), int0);
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong("VF(9Q66161", (-349), 0L, (-2825), 71);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      long long0 = Conversion.hexToLong("SWAoRthgf5VLBTz/1 L", (-349), 0L, 0, (-349));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      long long0 = Conversion.hexToLong("", (-1), 1816L, (short)0, 0);
      assertEquals(1816L, long0);
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong("SWAoRthgf5VLBTz/1 L", (-349), 1108L, 61, 857);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, (-3192), (short) (-787), 4762, 85);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-150), (short)96, (short)96, (-150));
      assertEquals((short)96, short0);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      short short0 = Conversion.byteArrayToShort(byteArray0, (byte)32, (byte) (-69), 28, (byte) (-69));
      assertEquals((short) (-69), short0);
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, 0, (-1943), 65535, 65535);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-30), (byte) (-114), (-955), (byte) (-114));
      assertEquals((-114L), long0);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (-1727));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1727
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      long long0 = Conversion.byteArrayToLong(byteArray0, 52, 65535L, (byte)65, 0);
      assertEquals(65535L, long0);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, 15, (byte)1, 81, 1682);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      short[] shortArray0 = new short[5];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short)0, (-2173), 0, 1);
      assertEquals((-65536), int0);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      short[] shortArray0 = new short[9];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short)14, (short)14, 41, (short) (-1));
      assertEquals(14, int0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      short[] shortArray0 = new short[9];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (short)0, (short)0, (short) (-5215), 2834);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      short[] shortArray0 = new short[2];
      int int0 = Conversion.shortArrayToInt(shortArray0, (short) (-1), (short)14, (-909), 0);
      assertEquals(14, int0);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      short[] shortArray0 = new short[1];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, 63, 816L, (short)329, 86);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      short[] shortArray0 = new short[8];
      long long0 = Conversion.shortArrayToLong(shortArray0, 441, 0L, (-2938), 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      short[] shortArray0 = new short[2];
      long long0 = Conversion.shortArrayToLong(shortArray0, (-458), 0L, 0, (-1));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      int[] intArray0 = new int[3];
      long long0 = Conversion.intArrayToLong(intArray0, 1, 1232L, 1, 1);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      int[] intArray0 = new int[9];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, 16277, 1177L, 14, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 16277
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      int[] intArray0 = new int[2];
      long long0 = Conversion.intArrayToLong(intArray0, 0, 255L, 7, (byte)0);
      assertEquals(255L, long0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      int[] intArray0 = new int[0];
      long long0 = Conversion.intArrayToLong(intArray0, 0, 191L, 0, 0);
      assertEquals(191L, long0);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      int[] intArray0 = new int[0];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, (-1), (-1L), 65, 75);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigitMsb0(165);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: 165
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigit(1155);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: 1155
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex((-1L), (-3883), "A53-`;>IO`)TFb#[B", (-848), 93);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -848
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[4] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 1155);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[0] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[2] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 1);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (int) (short)1);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (int) (short)1);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[19];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=19
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=1, srcPos=0
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 2821);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2821
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('f');
      assertTrue(Arrays.equals(new boolean[] {true, true, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('c');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('Y');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Y' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('T');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'T' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('S');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'S' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('K');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'K' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary(';');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ';' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('.');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '.' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('8');
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 2162, 2162, 0, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2162
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('s');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 's' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('c');
      assertTrue(Arrays.equals(new boolean[] {false, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('K');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'K' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('A');
      assertTrue(Arrays.equals(new boolean[] {false, true, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('?');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '?' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('8');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('\\');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '\\' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('[');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '[' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('V');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'V' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('D');
      assertEquals(11, int0);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('6');
      assertEquals(6, int0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('+');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '+' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToInt('{');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '{' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      short short0 = Conversion.hexToShort("M*CD5e)\"g7S9", 3, (short) (-1851), (short) (-1851), 3);
      assertEquals((short) (-13403), short0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      Conversion conversion0 = new Conversion();
  }
}
