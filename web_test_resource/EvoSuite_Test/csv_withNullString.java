/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:41:16 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.PipedReader;
import java.io.PipedWriter;
import java.io.Reader;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.Locale;
import javax.sql.rowset.RowSetMetaDataImpl;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.DuplicateHeaderMode;
import org.apache.commons.csv.QuoteMode;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.evosuite.runtime.mock.java.io.MockFileOutputStream;
import org.evosuite.runtime.mock.java.io.MockFileWriter;
import org.evosuite.runtime.mock.java.io.MockPrintStream;
import org.evosuite.runtime.mock.java.io.MockPrintWriter;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVFormat_ESTest extends CSVFormat_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      String[] stringArray0 = new String[7];
      stringArray0[0] = "9Ekz#TF*f=";
      MockFileOutputStream mockFileOutputStream0 = new MockFileOutputStream("Z3G'");
      MockPrintWriter mockPrintWriter0 = new MockPrintWriter(mockFileOutputStream0);
      cSVFormat0.printRecord(mockPrintWriter0, stringArray0);
      assertTrue(cSVFormat0.isEscapeCharacterSet());
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('\'');
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(true);
      assertEquals('\'', (char)cSVFormat2.getCommentMarker());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(true);
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(true);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim(true);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withTrim(true);
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Q');
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAutoFlush());
      assertEquals("Q", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getTrim());
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreEmptyLines(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withTrim();
      assertTrue(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withTrim();
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(false);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(false);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Oracle;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withAutoFlush(true);
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter();
      assertTrue(cSVFormat2.getAutoFlush());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.PostgreSQLText;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withSystemRecordSeparator();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('%');
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(false);
      assertFalse(cSVFormat2.getSkipHeaderRecord());
      assertEquals('%', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      CSVFormat cSVFormat3 = cSVFormat2.withSkipHeaderRecord(true);
      assertTrue(cSVFormat3.getTrailingDelimiter());
      assertTrue(cSVFormat3.equals((Object)cSVFormat2));
      assertTrue(cSVFormat3.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(true);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord(false);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Q');
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("Delimiter=<,> QuoteChar=<\"> RecordSeparator=<\r\n> SkipHeaderRecord:false");
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertEquals("Q", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('\u001A');
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("RecordSeparator=<");
      assertEquals('\u001A', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("MPM:a");
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("9Ekz#TF*f=");
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator("o@R");
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator("MongoDBCsv");
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('\'');
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('|');
      assertEquals("|", cSVFormat2.getRecordSeparator());
      assertEquals('\'', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('v');
      assertEquals("v", cSVFormat2.getRecordSeparator());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('|');
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals("|", cSVFormat2.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withRecordSeparator('v');
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertEquals("v", cSVFormat2.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Q');
      CSVFormat cSVFormat1 = cSVFormat0.withRecordSeparator('9');
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertEquals("9", cSVFormat1.getRecordSeparator());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertEquals("Q", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat1 = cSVFormat0.MYSQL.withQuoteMode(quoteMode0);
      assertEquals(QuoteMode.ALL, cSVFormat1.getQuoteMode());
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      QuoteMode quoteMode0 = QuoteMode.MINIMAL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      QuoteMode quoteMode0 = QuoteMode.MINIMAL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      QuoteMode quoteMode0 = QuoteMode.MINIMAL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withIgnoreHeaderCase();
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat cSVFormat2 = cSVFormat1.withQuoteMode(quoteMode0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      QuoteMode quoteMode0 = QuoteMode.ALL;
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withQuoteMode(quoteMode0);
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Character character0 = Character.valueOf('\"');
      CSVFormat cSVFormat1 = cSVFormat0.withQuote(character0);
      assertTrue(cSVFormat1.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      Character character0 = Character.valueOf('j');
      CSVFormat cSVFormat1 = cSVFormat0.withQuote(character0);
      assertNull(cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      Character character0 = Character.valueOf('Z');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withQuote((Character) null);
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withQuote('!');
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertEquals('!', (char)cSVFormat2.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withQuote('^');
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertEquals('^', (char)cSVFormat2.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('|');
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("");
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertEquals("|", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withNullString("");
      assertFalse(cSVFormat1.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withNullString("o@R");
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces(true);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces(true);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      Character character0 = new Character('Y');
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreHeaderCase(false);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase(true);
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreHeaderCase();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Character character0 = Character.valueOf('{');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreSurroundingSpaces(false);
      CSVFormat cSVFormat3 = cSVFormat2.withIgnoreEmptyLines(false);
      assertFalse(cSVFormat3.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat3.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Oracle;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines(true);
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines(false);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreEmptyLines(true);
      Character character0 = Character.valueOf('\"');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines(false);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withIgnoreEmptyLines(true);
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines();
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.EXCEL;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Character character0 = Character.valueOf('{');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(false);
      String[] stringArray0 = new String[8];
      CSVFormat cSVFormat3 = cSVFormat2.withHeaderComments(stringArray0);
      assertFalse(cSVFormat3.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withIgnoreHeaderCase();
      Object[] objectArray0 = new Object[2];
      CSVFormat cSVFormat2 = cSVFormat1.withHeaderComments(objectArray0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withHeaderComments((Object[]) null);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Object[] objectArray0 = new Object[2];
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withHeaderComments(objectArray0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('}');
      Object[] objectArray0 = new Object[0];
      cSVFormat0.EXCEL.withHeaderComments(objectArray0);
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertEquals('}', cSVFormat0.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('\'');
      String[] stringArray0 = new String[2];
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(stringArray0);
      assertEquals('\'', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      String[] stringArray0 = new String[3];
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(stringArray0);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      String[] stringArray0 = new String[1];
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(stringArray0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreHeaderCase();
      String[] stringArray0 = new String[0];
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(stringArray0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('%');
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      CSVFormat cSVFormat3 = cSVFormat2.withIgnoreEmptyLines();
      assertEquals('%', (char)cSVFormat3.getCommentMarker());
      assertTrue(cSVFormat3.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertEquals("\\N", cSVFormat1.getNullString());
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreHeaderCase();
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(resultSet0);
      assertNotSame(cSVFormat1, cSVFormat0);
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('#');
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn(rowSetMetaDataImpl0).when(resultSet0).getMetaData();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(resultSet0);
      assertEquals('#', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(resultSet0);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('}');
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(class0);
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getAutoFlush());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getTrailingDelimiter());
      assertEquals('}', cSVFormat2.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(class0);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(class0);
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      CSVFormat cSVFormat2 = cSVFormat1.withFirstRecordAsHeader();
      assertTrue(cSVFormat2.getTrim());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withFirstRecordAsHeader();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Excel;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      assertTrue(cSVFormat1.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreHeaderCase();
      Character character0 = Character.valueOf('1');
      CSVFormat cSVFormat2 = cSVFormat1.withEscape(character0);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withEscape((Character) null);
      assertNull(cSVFormat1.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      Character character0 = new Character('0');
      CSVFormat cSVFormat2 = cSVFormat1.withEscape(character0);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      Character character0 = new Character('0');
      CSVFormat cSVFormat2 = cSVFormat1.withEscape(character0);
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withEscape('b');
      assertEquals('b', (char)cSVFormat2.getEscapeCharacter());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withEscape('i');
      assertEquals('i', (char)cSVFormat1.getEscapeCharacter());
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('}');
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withEscape('Z');
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.getTrim());
      assertEquals('Z', (char)cSVFormat1.getEscapeCharacter());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertEquals('}', cSVFormat0.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('}');
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('');
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertEquals('', cSVFormat1.getDelimiter());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getAutoFlush());
      assertEquals("}", cSVFormat0.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('4');
      assertEquals("4", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD_CSV.withDelimiter('6');
      assertEquals('6', cSVFormat1.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.Excel;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withDelimiter('/');
      assertEquals("/", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Q');
      Character character0 = Character.valueOf('I');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertEquals("Q", cSVFormat1.getDelimiterString());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker((Character) null);
      assertFalse(cSVFormat1.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      Character character0 = Character.valueOf('g');
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker(character0);
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      Character character0 = new Character('Y');
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withFirstRecordAsHeader();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      Character character0 = new Character(')');
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker(character0);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('}');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('Z');
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertEquals('Z', (char)cSVFormat1.getCommentMarker());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
      assertEquals("}", cSVFormat1.getDelimiterString());
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('7');
      assertEquals('7', (char)cSVFormat2.getCommentMarker());
      assertTrue(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      CSVFormat cSVFormat2 = cSVFormat1.withCommentMarker('7');
      assertEquals('7', (char)cSVFormat2.getCommentMarker());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('}');
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      CSVFormat cSVFormat2 = cSVFormat1.withAutoFlush(true);
      assertFalse(cSVFormat2.getIgnoreHeaderCase());
      assertEquals("}", cSVFormat2.getDelimiterString());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertFalse(cSVFormat2.equals((Object)cSVFormat1));
      assertFalse(cSVFormat2.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat2.getTrim());
      assertFalse(cSVFormat2.isNullStringSet());
      assertFalse(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.withTrailingDelimiter(true);
      CSVFormat cSVFormat3 = cSVFormat2.withAutoFlush(true);
      assertTrue(cSVFormat3.getIgnoreSurroundingSpaces());
      assertTrue(cSVFormat3.getTrailingDelimiter());
      assertTrue(cSVFormat3.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withAutoFlush(false);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertFalse(cSVFormat2.getAutoFlush());
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat cSVFormat2 = cSVFormat1.withHeader(resultSet0);
      String[] stringArray0 = new String[7];
      CSVFormat cSVFormat3 = cSVFormat2.withHeader(stringArray0);
      assertTrue(cSVFormat3.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(false);
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('0');
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertEquals('0', cSVFormat1.getDelimiter());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD;
      Character character0 = Character.valueOf('{');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord(true);
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.TDF.withIgnoreEmptyLines();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertTrue(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowMissingColumnNames();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Q');
      cSVFormat0.TDF.withAllowDuplicateHeaderNames(true);
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertEquals("Q", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(true);
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, cSVFormat2.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.INFORMIX_UNLOAD.withAllowDuplicateHeaderNames();
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('#');
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames();
      assertEquals('#', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames();
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.EXCEL.withAllowDuplicateHeaderNames();
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      String string0 = cSVFormat0.trim((String) null);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      String string0 = cSVFormat0.trim("");
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.PostgreSQLText;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.DEFAULT.withIgnoreEmptyLines();
      cSVFormat1.getNullString();
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Q');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces(true);
      boolean boolean0 = cSVFormat1.getIgnoreSurroundingSpaces();
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertFalse(cSVFormat1.getIgnoreEmptyLines());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getAutoFlush());
      assertEquals("Q", cSVFormat1.getDelimiterString());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Q');
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreEmptyLines();
      boolean boolean0 = cSVFormat1.getIgnoreEmptyLines();
      assertFalse(cSVFormat1.getTrim());
      assertFalse(cSVFormat1.isNullStringSet());
      assertFalse(cSVFormat1.getAllowMissingColumnNames());
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(cSVFormat1.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat1.getTrailingDelimiter());
      assertFalse(cSVFormat1.getAutoFlush());
      assertFalse(cSVFormat1.getSkipHeaderRecord());
      assertFalse(cSVFormat1.getIgnoreHeaderCase());
      assertEquals('Q', cSVFormat1.getDelimiter());
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      Character character0 = cSVFormat0.getEscapeCharacter();
      assertEquals('\"', (char)character0);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Object[] objectArray0 = new Object[0];
      String string0 = cSVFormat0.DEFAULT.format(objectArray0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      CSVFormat cSVFormat2 = cSVFormat1.copy();
      assertTrue(cSVFormat2.getIgnoreSurroundingSpaces());
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      CSVFormat cSVFormat3 = cSVFormat2.copy();
      assertTrue(cSVFormat3.getIgnoreHeaderCase());
      assertTrue(cSVFormat3.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      CSVFormat cSVFormat1 = cSVFormat0.copy();
      assertFalse(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      CSVFormat cSVFormat2 = cSVFormat1.copy();
      assertTrue(cSVFormat2.getAllowMissingColumnNames());
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      Locale.FilteringMode[] locale_FilteringModeArray0 = new Locale.FilteringMode[4];
      Locale.FilteringMode[] locale_FilteringModeArray1 = CSVFormat.clone(locale_FilteringModeArray0);
      assertEquals(4, locale_FilteringModeArray1.length);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Object[] objectArray0 = new Object[8];
      // Undeclared exception!
      try { 
        cSVFormat0.INFORMIX_UNLOAD.printRecord((Appendable) null, objectArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('5');
      MockFile mockFile0 = new MockFile("");
      Charset charset0 = Charset.defaultCharset();
      try { 
        cSVFormat0.print((File) mockFile0, charset0);
        fail("Expecting exception: FileNotFoundException");
      
      } catch(FileNotFoundException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockFileOutputStream", e);
      }
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      // Undeclared exception!
      try { 
        cSVFormat0.parse((Reader) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // reader
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      String[] stringArray0 = new String[7];
      PipedWriter pipedWriter0 = new PipedWriter();
      PipedReader pipedReader0 = new PipedReader(pipedWriter0);
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(stringArray0);
      // Undeclared exception!
      try { 
        cSVFormat1.parse(pipedReader0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // A header name is missing in [null, null, null, null, null, null, null]
         //
         verifyException("org.apache.commons.csv.CSVParser", e);
      }
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      String[] stringArray0 = new String[2];
      CSVFormat cSVFormat1 = cSVFormat0.DEFAULT.withHeader(stringArray0);
      String string0 = cSVFormat1.toString();
      assertEquals("Delimiter=<,> QuoteChar=<\"> RecordSeparator=<\r\n> EmptyLines:ignored SkipHeaderRecord:false Header:[null, null]", string0);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      String string0 = cSVFormat0.toString();
      assertEquals("Delimiter=<,> QuoteChar=<\"> QuoteMode=<ALL_NON_NULL> NullString=<> RecordSeparator=<\n> SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      MockFileWriter mockFileWriter0 = new MockFileWriter("MongoDBCsv", true);
      cSVFormat0.EXCEL.println(mockFileWriter0);
      assertFalse(cSVFormat0.getTrailingDelimiter());
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      Charset charset0 = Charset.defaultCharset();
      ByteBuffer byteBuffer0 = ByteBuffer.allocateDirect(92);
      CharBuffer charBuffer0 = charset0.decode(byteBuffer0);
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      cSVFormat0.print((Object) null, (Appendable) charBuffer0, true);
      assertEquals(92, charBuffer0.remaining());
      assertEquals("\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000", charBuffer0.toString());
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Charset charset0 = Charset.defaultCharset();
      ByteBuffer byteBuffer0 = ByteBuffer.allocateDirect(92);
      CharBuffer charBuffer0 = charset0.decode(byteBuffer0);
      cSVFormat0.RFC4180.print((Object) cSVFormat0, (Appendable) charBuffer0, true);
      assertEquals(43, charBuffer0.position());
      assertEquals(49, charBuffer0.length());
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.isCommentMarkerSet();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.TDF;
      String[] stringArray0 = cSVFormat0.getHeader();
      assertNull(stringArray0);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Character character0 = Character.valueOf('Z');
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote(character0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      RowSetMetaDataImpl rowSetMetaDataImpl0 = new RowSetMetaDataImpl();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSetMetaData) rowSetMetaDataImpl0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSetMetaData) null);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      ResultSet resultSet0 = mock(ResultSet.class, new ViolatedAssumptionAnswer());
      doReturn((ResultSetMetaData) null).when(resultSet0).getMetaData();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(resultSet0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      Class<Locale.FilteringMode> class0 = Locale.FilteringMode.class;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(class0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      Character character0 = Character.valueOf('Z');
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape(character0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter("The quoteChar cannot be a line break");
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setSkipHeaderRecord(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator('y');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      String[] stringArray0 = new String[3];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader(stringArray0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.PostgreSQLCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      String string0 = cSVFormat0.getDelimiterString();
      assertEquals(",", string0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Q');
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      cSVFormat_Builder0.setAllowMissingColumnNames(true);
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertEquals("Q", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrim());
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setEscape('Z');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      Character character0 = cSVFormat0.getCommentMarker();
      assertNull(character0);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuote('0');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreHeaderCase(true);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDelimiter('9');
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreEmptyLines(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.getIgnoreHeaderCase();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setIgnoreSurroundingSpaces(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrailingDelimiter(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      DuplicateHeaderMode duplicateHeaderMode0 = DuplicateHeaderMode.ALLOW_EMPTY;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setDuplicateHeaderMode(duplicateHeaderMode0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker('y');
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setQuoteMode(quoteMode0);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create(cSVFormat0);
      Object[] objectArray0 = new Object[5];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeaderComments(objectArray0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.getTrim();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Q');
      boolean boolean0 = cSVFormat0.getIgnoreSurroundingSpaces();
      assertFalse(cSVFormat0.getIgnoreEmptyLines());
      assertFalse(cSVFormat0.getAutoFlush());
      assertEquals("Q", cSVFormat0.getDelimiterString());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(boolean0);
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(cSVFormat0.isNullStringSet());
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.newFormat('Q');
      boolean boolean0 = cSVFormat0.getIgnoreEmptyLines();
      assertFalse(cSVFormat0.getAutoFlush());
      assertFalse(cSVFormat0.getTrailingDelimiter());
      assertFalse(cSVFormat0.getSkipHeaderRecord());
      assertFalse(cSVFormat0.getIgnoreSurroundingSpaces());
      assertFalse(cSVFormat0.isNullStringSet());
      assertFalse(cSVFormat0.getTrim());
      assertFalse(boolean0);
      assertFalse(cSVFormat0.getAllowMissingColumnNames());
      assertFalse(cSVFormat0.getIgnoreHeaderCase());
      assertEquals('Q', cSVFormat0.getDelimiter());
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Character character0 = cSVFormat0.getEscapeCharacter();
      assertNull(character0);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.ORACLE.withAllowDuplicateHeaderNames(true);
      assertTrue(cSVFormat1.getAllowDuplicateHeaderNames());
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowDuplicateHeaderNames(false);
      assertEquals(DuplicateHeaderMode.ALLOW_EMPTY, cSVFormat1.getDuplicateHeaderMode());
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      QuoteMode quoteMode0 = QuoteMode.NONE;
      // Undeclared exception!
      try { 
        cSVFormat0.TDF.withQuoteMode(quoteMode0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No quotes mode set but no escape character is set
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      CSVFormat cSVFormat1 = cSVFormat0.withEscape('b');
      Character character0 = new Character('b');
      // Undeclared exception!
      try { 
        cSVFormat1.withCommentMarker(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start and the escape character cannot be the same ('b')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      Character character0 = Character.valueOf('\"');
      // Undeclared exception!
      try { 
        cSVFormat0.withCommentMarker(character0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The comment start character and the quoteChar cannot be the same ('\"')
         //
         verifyException("org.apache.commons.csv.CSVFormat", e);
      }
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBTsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      String string0 = cSVFormat0.trim("`@");
      assertEquals("`@", string0);
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      String string0 = cSVFormat1.toString();
      assertEquals("Delimiter=<,> Escape=<\\> QuoteChar=<\"> QuoteMode=<MINIMAL> NullString=<\\N> RecordSeparator=<\n> SurroundingSpaces:ignored SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.DEFAULT.withRecordSeparator((String) null);
      Object[] objectArray0 = new Object[9];
      objectArray0[4] = (Object) cSVFormat1;
      CSVFormat cSVFormat2 = cSVFormat1.POSTGRESQL_TEXT.withHeaderComments(objectArray0);
      assertFalse(cSVFormat1.isNullStringSet());
      assertTrue(cSVFormat1.isQuoteCharacterSet());
      assertFalse(cSVFormat2.isCommentMarkerSet());
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MONGODB_TSV;
      assertTrue(cSVFormat0.isQuoteCharacterSet());
      
      MockFileWriter mockFileWriter0 = new MockFileWriter("MongoDBCsv", true);
      Object[] objectArray0 = new Object[7];
      objectArray0[2] = (Object) "MongoDBCsv";
      cSVFormat0.MYSQL.printRecord(mockFileWriter0, objectArray0);
      assertTrue(cSVFormat0.isEscapeCharacterSet());
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      String[] stringArray0 = new String[3];
      stringArray0[0] = " SkipHeaderRecord:";
      CSVFormat cSVFormat1 = cSVFormat0.withHeader(stringArray0);
      cSVFormat1.printer();
      assertTrue(cSVFormat1.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreHeaderCase();
      String string0 = cSVFormat1.toString();
      assertEquals("Delimiter=<\t> Escape=<\\> QuoteMode=<ALL_NON_NULL> NullString=<\\N> RecordSeparator=<\n> IgnoreHeaderCase:ignored SkipHeaderRecord:false", string0);
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.getAllowDuplicateHeaderNames();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_TEXT;
      // Undeclared exception!
      try { 
        cSVFormat0.format((Object[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.POSTGRESQL_TEXT.withIgnoreHeaderCase();
      boolean boolean0 = cSVFormat0.equals(cSVFormat1);
      assertFalse(boolean0);
      assertTrue(cSVFormat1.getIgnoreHeaderCase());
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('#');
      boolean boolean0 = cSVFormat1.INFORMIX_UNLOAD_CSV.equals(cSVFormat1);
      assertEquals('#', (char)cSVFormat1.getCommentMarker());
      assertFalse(cSVFormat0.equals((Object)cSVFormat1));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withAllowMissingColumnNames(true);
      boolean boolean0 = cSVFormat0.equals(cSVFormat1);
      assertFalse(cSVFormat1.equals((Object)cSVFormat0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.equals(cSVFormat0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      boolean boolean0 = cSVFormat0.INFORMIX_UNLOAD_CSV.equals(cSVFormat0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      assertFalse(cSVFormat0.isEscapeCharacterSet());
      
      Object[] objectArray0 = new Object[2];
      objectArray0[0] = (Object) cSVFormat0;
      cSVFormat0.format(objectArray0);
      assertTrue(cSVFormat0.isQuoteCharacterSet());
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setCommentMarker((Character) null);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      boolean boolean0 = CSVFormat.isBlank("`@");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeader((ResultSet) null);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      // Undeclared exception!
      try { 
        cSVFormat_Builder0.setDelimiter("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The delimiter cannot be empty
         //
         verifyException("org.apache.commons.csv.CSVFormat$Builder", e);
      }
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withSkipHeaderRecord();
      Character character0 = Character.valueOf('.');
      CSVFormat cSVFormat2 = cSVFormat1.withQuote(character0);
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.PostgreSQLText;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      String string0 = cSVFormat0.getNullString();
      assertEquals("\\N", string0);
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withSystemRecordSeparator();
      assertEquals("\n", cSVFormat1.getRecordSeparator());
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.PostgreSQLCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      MockFile mockFile0 = new MockFile("!PR6/sY=wv@uJ&w");
      Charset charset0 = Charset.defaultCharset();
      CSVPrinter cSVPrinter0 = cSVFormat0.print((File) mockFile0, charset0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      DuplicateHeaderMode duplicateHeaderMode0 = cSVFormat0.getDuplicateHeaderMode();
      assertEquals(DuplicateHeaderMode.ALLOW_ALL, duplicateHeaderMode0);
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      // Undeclared exception!
      try { 
        CSVFormat.valueOf("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.csv.CSVFormat.Predefined.
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreSurroundingSpaces();
      boolean boolean0 = cSVFormat0.equals(cSVFormat1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBTsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      ByteArrayOutputStream byteArrayOutputStream0 = new ByteArrayOutputStream();
      MockPrintStream mockPrintStream0 = new MockPrintStream(byteArrayOutputStream0);
      CSVPrinter cSVPrinter0 = cSVFormat0.POSTGRESQL_TEXT.print((Appendable) mockPrintStream0);
      assertNotNull(cSVPrinter0);
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.ORACLE;
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter(true);
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      CSVFormat cSVFormat3 = cSVFormat2.copy();
      assertTrue(cSVFormat3.getTrailingDelimiter());
      assertTrue(cSVFormat3.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      cSVFormat0.hashCode();
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      Charset charset0 = Charset.defaultCharset();
      ByteBuffer byteBuffer0 = ByteBuffer.allocateDirect(92);
      CharBuffer charBuffer0 = charset0.decode(byteBuffer0);
      CharSequence charSequence0 = CSVFormat.trim(charBuffer0);
      CharSequence charSequence1 = CSVFormat.trim(charSequence0);
      assertSame(charSequence1, charSequence0);
      assertEquals("", charSequence1.toString());
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      boolean boolean0 = cSVFormat0.getAllowMissingColumnNames();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withTrim();
      assertTrue(cSVFormat1.getTrim());
  }

  @Test(timeout = 4000)
  public void test219()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      PipedWriter pipedWriter0 = new PipedWriter();
      PipedReader pipedReader0 = new PipedReader(pipedWriter0);
      CSVParser cSVParser0 = cSVFormat0.parse(pipedReader0);
      assertNull(cSVParser0.getTrailerComment());
  }

  @Test(timeout = 4000)
  public void test220()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBTsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      Character character0 = Character.valueOf('1');
      CSVFormat cSVFormat1 = cSVFormat0.withEscape(character0);
      assertTrue(cSVFormat1.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test221()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      Character character0 = Character.valueOf('o');
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker(character0);
      CSVFormat cSVFormat2 = cSVFormat1.withSkipHeaderRecord();
      assertTrue(cSVFormat2.getSkipHeaderRecord());
  }

  @Test(timeout = 4000)
  public void test222()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      String string0 = cSVFormat0.getRecordSeparator();
      assertEquals("\r\n", string0);
  }

  @Test(timeout = 4000)
  public void test223()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      char char0 = cSVFormat0.getDelimiter();
      assertEquals(',', char0);
  }

  @Test(timeout = 4000)
  public void test224()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setAutoFlush(false);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test225()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat cSVFormat0 = cSVFormat_Builder0.build();
      CSVFormat cSVFormat1 = cSVFormat0.withTrailingDelimiter();
      CSVFormat cSVFormat2 = cSVFormat1.withIgnoreEmptyLines(false);
      assertTrue(cSVFormat2.getTrailingDelimiter());
      assertFalse(cSVFormat2.getIgnoreEmptyLines());
  }

  @Test(timeout = 4000)
  public void test226()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.RFC4180;
      CSVFormat cSVFormat1 = cSVFormat0.withCommentMarker('%');
      CSVFormat cSVFormat2 = cSVFormat1.copy();
      assertEquals('%', (char)cSVFormat2.getCommentMarker());
  }

  @Test(timeout = 4000)
  public void test227()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat cSVFormat1 = cSVFormat0.withQuote('(');
      assertEquals('(', (char)cSVFormat1.getQuoteCharacter());
  }

  @Test(timeout = 4000)
  public void test228()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.POSTGRESQL_CSV;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      String[] stringArray0 = new String[0];
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setHeaderComments(stringArray0);
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test229()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setTrim(false);
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test230()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.DEFAULT;
      CSVFormat.Builder cSVFormat_Builder0 = cSVFormat0.builder();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setRecordSeparator("");
      assertSame(cSVFormat_Builder1, cSVFormat_Builder0);
  }

  @Test(timeout = 4000)
  public void test231()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.PostgreSQLCsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      CSVFormat cSVFormat1 = cSVFormat0.withFirstRecordAsHeader();
      Object[] objectArray0 = new Object[3];
      objectArray0[1] = (Object) cSVFormat1;
      String[] stringArray0 = CSVFormat.toStringArray(objectArray0);
      assertEquals(3, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test232()  throws Throwable  {
      CSVFormat.Builder cSVFormat_Builder0 = CSVFormat.Builder.create();
      CSVFormat.Builder cSVFormat_Builder1 = cSVFormat_Builder0.setNullString("&j");
      assertSame(cSVFormat_Builder0, cSVFormat_Builder1);
  }

  @Test(timeout = 4000)
  public void test233()  throws Throwable  {
      CSVFormat.Predefined cSVFormat_Predefined0 = CSVFormat.Predefined.MongoDBTsv;
      CSVFormat cSVFormat0 = cSVFormat_Predefined0.getFormat();
      QuoteMode quoteMode0 = QuoteMode.ALL_NON_NULL;
      CSVFormat cSVFormat1 = cSVFormat0.withQuoteMode(quoteMode0);
      assertEquals(QuoteMode.ALL_NON_NULL, cSVFormat1.getQuoteMode());
  }

  @Test(timeout = 4000)
  public void test234()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVFormat cSVFormat1 = cSVFormat0.withIgnoreHeaderCase();
      CSVFormat cSVFormat2 = cSVFormat1.withAllowDuplicateHeaderNames(false);
      assertTrue(cSVFormat2.getIgnoreHeaderCase());
      assertFalse(cSVFormat2.getAllowDuplicateHeaderNames());
  }
}
