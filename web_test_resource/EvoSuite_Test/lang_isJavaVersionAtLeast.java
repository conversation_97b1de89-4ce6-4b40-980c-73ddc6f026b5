/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:12:02 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.File;
import org.apache.commons.lang3.JavaVersion;
import org.apache.commons.lang3.SystemUtils;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.testdata.EvoSuiteFile;
import org.evosuite.runtime.testdata.FileSystemHandling;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class SystemUtils_ESTest extends SystemUtils_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSVersionMatch("Java Virtual Machine Specification", "Java Virtual Machine Specification");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSNameMatch("user.home", "user.home");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      boolean boolean0 = SystemUtils.isJavaVersionMatch("Java Vitual achine Specification", "Java Vitual achine Specification");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      JavaVersion javaVersion0 = JavaVersion.JAVA_0_9;
      boolean boolean0 = SystemUtils.isJavaVersionAtMost(javaVersion0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      JavaVersion javaVersion0 = JavaVersion.JAVA_10;
      boolean boolean0 = SystemUtils.isJavaVersionAtLeast(javaVersion0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      EvoSuiteFile evoSuiteFile0 = new EvoSuiteFile("/Users/<USER>");
      FileSystemHandling.setPermissions(evoSuiteFile0, false, false, false);
      File file0 = SystemUtils.getUserHome();
      assertTrue(file0.canRead());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      EvoSuiteFile evoSuiteFile0 = new EvoSuiteFile("/Users/<USER>/Desktop/java-project/commons-lang-rel-commons-lang-3.10");
      FileSystemHandling.setPermissions(evoSuiteFile0, true, true, false);
      File file0 = SystemUtils.getUserDir();
      assertEquals(0L, file0.getUsableSpace());
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      EvoSuiteFile evoSuiteFile0 = new EvoSuiteFile("/var/folders/sl/3mfrgmdj2n1cl2km06yxm9jh0000gn/T");
      FileSystemHandling.setPermissions(evoSuiteFile0, true, false, true);
      File file0 = SystemUtils.getJavaIoTmpDir();
      assertTrue(file0.isDirectory());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      EvoSuiteFile evoSuiteFile0 = new EvoSuiteFile("/Library/Java/JavaVirtualMachines/temurin-8.jdk/Contents/Home/jre");
      FileSystemHandling.appendLineToFile(evoSuiteFile0, "CNOpenJDK 64-Bit Server VM");
      File file0 = SystemUtils.getJavaHome();
      assertTrue(file0.isFile());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      String string0 = SystemUtils.getEnvironmentVariable("m", (String) null);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      String string0 = SystemUtils.getEnvironmentVariable("", "");
      assertNotNull(string0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      // Undeclared exception!
      try { 
        SystemUtils.isOSVersionMatch("8?F>h^1(at", (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.SystemUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      // Undeclared exception!
      try { 
        SystemUtils.isOSNameMatch("user.home", (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      // Undeclared exception!
      try { 
        SystemUtils.isOSMatch("true", "1.8", (String) null, "Temurin");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      // Undeclared exception!
      try { 
        SystemUtils.isJavaVersionMatch("Aborting to protect against StackOverflowError - output of one loop is the input of another", (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      // Undeclared exception!
      try { 
        SystemUtils.getEnvironmentVariable((String) null, (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.lang.ProcessEnvironment$Variable", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSVersionMatch("", ":9O=-hGttcEl;5Y6[7");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSNameMatch((String) null, "99.0");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSMatch("^cj^:L+q/ABg|{EPc.u", "mac", "^cj^:L+q/ABg|{EPc.u", "mac");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSMatch(":9O=-hGttcEl;5Y6[7", "9*&0&VR%C", "", "pmIf[XpS/.f:OIA2xAk");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSMatch("mixed mode", (String) null, "\n", "t?");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSMatch("\"O8Ls", "4/rq@s~ne", "p{InCombiningDiacriticalMarks}+", "&{(?H");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      boolean boolean0 = SystemUtils.isOSMatch((String) null, "Windows 2000", "3Humq#Bk]", "3Humq#Bk]");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      boolean boolean0 = SystemUtils.isJavaVersionMatch((String) null, (String) null);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      String string0 = SystemUtils.getEnvironmentVariable("\"}xega%?eWBX", "7");
      assertEquals("7", string0);
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      String string0 = SystemUtils.getHostName();
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      SystemUtils systemUtils0 = new SystemUtils();
      assertEquals("mac", systemUtils0.getUserName());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      boolean boolean0 = SystemUtils.isJavaAwtHeadless();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      File file0 = SystemUtils.getUserHome();
      assertTrue(file0.isDirectory());
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      File file0 = SystemUtils.getUserDir();
      assertTrue(file0.exists());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      File file0 = SystemUtils.getJavaHome();
      assertEquals(0L, file0.getTotalSpace());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      String string0 = SystemUtils.getUserName();
      assertEquals("mac", string0);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      JavaVersion javaVersion0 = JavaVersion.JAVA_1_2;
      boolean boolean0 = SystemUtils.isJavaVersionAtLeast(javaVersion0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      JavaVersion javaVersion0 = JavaVersion.JAVA_10;
      boolean boolean0 = SystemUtils.isJavaVersionAtMost(javaVersion0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      File file0 = SystemUtils.getJavaIoTmpDir();
      assertEquals("/var/folders/sl/3mfrgmdj2n1cl2km06yxm9jh0000gn", file0.getParent());
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      String string0 = SystemUtils.getUserName("FreeBSDMac OS X");
      assertEquals("mac", string0);
  }
}
