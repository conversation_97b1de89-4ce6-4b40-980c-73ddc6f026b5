/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 09:15:17 GMT 2025
 */

package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.Conversion;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.util.MockUUID;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class Conversion_ESTest extends Conversion_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      short[] shortArray0 = new short[0];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray((byte)0, 64, shortArray0, (short)2104, 1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("", 255, (short)71, 4508, (-882));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, 65535, 5, (byte)11, 98);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      Conversion.shortToByteArray((short)4, (-1583), (byte[]) null, (-1), (byte) (-127));
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      Conversion.shortToByteArray((short)0, (-377), byteArray0, (short)0, (-2261));
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      short[] shortArray0 = new short[3];
      Conversion.shortArrayToLong(shortArray0, 97, (short)0, 64, (short) (-1697));
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      short[] shortArray0 = new short[7];
      Conversion.shortArrayToInt(shortArray0, (-1401), 0, (short) (-12), (short) (-12));
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      short[] shortArray0 = new short[5];
      Conversion.shortArrayToInt(shortArray0, 4262, (short) (-1524), 4262, (short) (-1524));
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      Conversion.longToByteArray(1L, (-127), byteArray0, (-127), 0);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      Conversion.longToBinary((-236L), (-849), booleanArray0, 1, (-2210));
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      Conversion.intToShortArray(88, (-1), (short[]) null, 2, (-1));
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      short[] shortArray0 = new short[0];
      Conversion.intToShortArray(91, (byte)0, shortArray0, 90, 0);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      Conversion.intToHexDigit(9);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      Conversion.intToBinary(2750, (-983), booleanArray0, (-983), (-983));
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      int[] intArray0 = new int[1];
      Conversion.intArrayToLong(intArray0, 54, (short)0, 1065, (short)0);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      int[] intArray0 = new int[1];
      Conversion.intArrayToLong(intArray0, 54, 4L, 1065, (short)0);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      Conversion.hexToShort("'G]>C}\"EPMs]U1/FU", 558, (short)2104, (-599), 0);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      Conversion.hexToLong("", 3510, (-1L), (short)2227, (-2730));
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      Conversion.hexToInt("xk", 1594, (short)0, (-2075), (byte) (-40));
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      Conversion.hexToInt((String) null, (-712), (-712), 0, (-712));
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      Conversion.hexToByte("", (short)0, (byte)0, (-1), (short)0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      Conversion.hexToByte("QP3NS", 49, (byte)4, 0, (-1093));
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      Conversion.byteToBinary((byte)80, 1, (boolean[]) null, 0, (-452));
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      Conversion.byteArrayToLong(byteArray0, 2763, (-741), (short) (-933), (-372));
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray((UUID) null, (byte[]) null, (-1), (-1093));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt((short[]) null, 788, 788, 100, 100);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      short[] shortArray0 = new short[3];
      // Undeclared exception!
      try { 
        Conversion.longToShortArray((-1L), 4, shortArray0, (short) (-2921), 4);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -2921
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToBinary(606L, 3, (boolean[]) null, 3, 3);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex((short)2104, (-2225), "", 92, 536);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 92
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong((int[]) null, 2018, 91, 84, 84);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)4, (byte)4, (String) null, 14, (-1851));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid((byte[]) null, 4141);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong((byte[]) null, 82, 82, 82, 102);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToLong((boolean[]) null, (-68), (-68), 0, 1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToInt((boolean[]) null, 421, 421, 421, (-1));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits((boolean[]) null, (-12));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit((boolean[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.binaryToByte((boolean[]) null, 57, (byte) (-59), (byte) (-59), 0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.longToByteArray(65535L, 1, byteArray0, (byte)65, (-1541));
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, 1572, (short)0, (short)0, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1572
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[4];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      booleanArray0[0] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, 11);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[0] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 2557);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0, (int) (short)0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      byte[] byteArray0 = new byte[20];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (byte)57);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Need at least 16 bytes for UUID
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("qXi@6\"Tp");
      byte[] byteArray0 = new byte[6];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, (-1221), 2728);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBytes is greater than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("");
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (byte)0, (byte)0);
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte) (-35), 1, booleanArray0, (byte) (-35), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -35
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      boolean[] booleanArray1 = Conversion.byteToBinary((byte) (-40), 923, booleanArray0, 14900, (-1079));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToBinary((byte)111, 66, (boolean[]) null, 779, 64);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)4692, (-1673), booleanArray0, 52, (byte)51);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 52
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      boolean[] booleanArray1 = Conversion.shortToBinary((byte) (-40), (short)0, booleanArray0, 9, (-1079));
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      boolean[] booleanArray1 = Conversion.shortToBinary((short)0, 0, booleanArray0, 0, (short)0);
      assertTrue(Arrays.equals(new boolean[] {false}, booleanArray1));
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.shortToBinary((short)713, (short)713, booleanArray0, (short)713, 2);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.intToBinary((-2261), (-347), booleanArray0, (-1028), 98);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1028
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      boolean[] booleanArray1 = Conversion.intToBinary(13, 32, booleanArray0, (-1177), (-1428));
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      boolean[] booleanArray1 = Conversion.intToBinary(0, 678, booleanArray0, (byte)49, 0);
      assertEquals(9, booleanArray1.length);
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.intToBinary(2698, 101, booleanArray0, 669, 68);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      // Undeclared exception!
      try { 
        Conversion.longToBinary((-111L), (-1698), booleanArray0, 99, 99);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 99
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.longToBinary(1L, (-2107), booleanArray0, (-1529), 165);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1529
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.longToBinary(3, 784, booleanArray0, 784, (byte) (-127));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      boolean[] booleanArray1 = Conversion.longToBinary(0L, 69, booleanArray0, 1, (-1154));
      assertSame(booleanArray1, booleanArray0);
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.byteToHex((byte)87, (byte)87, "(nShorts-1)*16+dstPos is greater or equal to than 32", (byte)87, (byte)87);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)0, 5255, "(nHexs-1)*4+dstPos is greater or equal to than 8", 5255, (byte)0);
      assertEquals("(nHexs-1)*4+dstPos is greater or equal to than 8", string0);
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      String string0 = Conversion.byteToHex((byte)1, 52, "(nHexs-1)*4+dstPos is greater or equal to than 8", 2763, (-741));
      assertEquals("(nHexs-1)*4+dstPos is greater or equal to than 8", string0);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)98, 2591, "", (short)0, (-2299));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((short)2876, (short)2876, "", (short)2876, (-174));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      String string0 = Conversion.shortToHex((short)2227, 50, "^gX2vo8eTmQvlA", 2, (short)0);
      assertEquals("^gX2vo8eTmQvlA", string0);
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.shortToHex((byte)1, (-292), "", 1, 56);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: 1
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      String string0 = Conversion.intToHex((-1), (-5015), "m=dNX4n33QTQcbEsa", 15, 86);
      assertEquals("m=dNX4n33QTQcbEffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", string0);
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      String string0 = Conversion.intToHex((byte)49, 0, "(nHexs-1)*4+dstPos is greater or equal to than 16", (-1255), (-2414));
      assertEquals("(nHexs-1)*4+dstPos is greater or equal to than 16", string0);
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      String string0 = Conversion.intToHex(923, '9', "", (short)2227, (short)0);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHex(5287, 5287, "na`s&Y=^u", (-1822), 5287);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      String string0 = Conversion.longToHex(9L, (-10), "", 1141, (-10));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(0L, 52, "A:!~w1?o~&", 52, 52);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      String string0 = Conversion.longToHex((-1469L), 1097, "30|xB", 0, 0);
      assertEquals("30|xB", string0);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToHex(2961L, 18, "", (-3980), 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
         //
         // String index out of range: -3980
         //
         verifyException("java.lang.AbstractStringBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((short) (-12), (-1093), byteArray0, (short) (-1482), 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1482
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.shortToByteArray((byte)67, 1, byteArray0, (-2378), 49);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      byte[] byteArray1 = Conversion.shortToByteArray((byte)112, 4537, byteArray0, (byte)112, (byte)0);
      assertSame(byteArray1, byteArray0);
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      byte[] byteArray0 = new byte[4];
      byte[] byteArray1 = Conversion.intToByteArray((short)1, (byte)1, byteArray0, 2531, (byte) (-127));
      assertEquals(4, byteArray1.length);
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      // Undeclared exception!
      try { 
        Conversion.intToByteArray((byte)106, (byte)0, byteArray0, (byte)10, (byte)106);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      UUID uUID0 = MockUUID.fromString("");
      byte[] byteArray0 = new byte[1];
      // Undeclared exception!
      try { 
        Conversion.uuidToByteArray(uUID0, byteArray0, (byte)4, (byte)4);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.longToByteArray(0L, (short)14, byteArray0, 92, 1873);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      UUID uUID0 = MockUUID.randomUUID();
      byte[] byteArray0 = new byte[1];
      byte[] byteArray1 = Conversion.uuidToByteArray(uUID0, byteArray0, (-1027), (-1027));
      assertSame(byteArray0, byteArray1);
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.intToShortArray((-157), (short)49, shortArray0, (short)1, (short)1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      short[] shortArray0 = new short[7];
      short[] shortArray1 = Conversion.intToShortArray(8, 8, shortArray0, (-1314), (short)0);
      assertSame(shortArray0, shortArray1);
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToShortArray(0L, 3, (short[]) null, 3, 3);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToShortArray((-2140L), 1612, (short[]) null, 1, 3);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToIntArray((short)0, (-655), (int[]) null, (-1177), 3);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.longToIntArray(99, (-1177), (int[]) null, 79, 2884);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+srcPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      byte byte0 = Conversion.binaryToByte(booleanArray0, (-2184), (byte)85, (byte)85, (-1835));
      assertEquals((byte)85, byte0);
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, 1484, (byte)2, (byte)2, (byte)2);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1484
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      byte byte0 = Conversion.binaryToByte(booleanArray0, (byte)18, (byte) (-127), (-1), 0);
      assertEquals((byte) (-127), byte0);
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[15];
      // Undeclared exception!
      try { 
        Conversion.binaryToByte(booleanArray0, (byte)80, (byte)14, (byte)80, 57);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      short short0 = Conversion.binaryToShort(booleanArray0, 0, (short) (-2836), 0, (short) (-2836));
      assertEquals((short) (-2836), short0);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, (byte) (-127), (short) (-127), (-2068), 51);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -127
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryToShort(booleanArray0, 78, (short) (-1482), 89, 2773);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      short short0 = Conversion.binaryToShort(booleanArray0, 0, (short)2834, 0, 67);
      assertEquals((short)2834, short0);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      short short0 = Conversion.binaryToShort(booleanArray0, 923, (short)0, 54, (short)0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[1];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, (short)1, (byte) (-127), (-430), 100);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[13];
      int int0 = Conversion.binaryToInt(booleanArray0, 997, (-2374), 997, 0);
      assertEquals((-2374), int0);
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, 1484, (byte)2, 1506, 1484);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nBools-1+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('a');
      int int0 = Conversion.binaryToInt(booleanArray0, (byte)0, (byte)1, (-1430), (-2466));
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte("(~qA.E7~$hbjf", (-246), (byte)12, (-246), (byte)12);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      byte byte0 = Conversion.hexToByte("(nHexs-1)*4+dstPos is greater or equal to than 8", 0, (byte) (-11), (byte)1, (-741));
      assertEquals((byte) (-11), byte0);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToByte((String) null, 5, (byte)0, 2038, 2898);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 8
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToShort("", (-1041), (byte) (-35), (short)14, 1);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      short short0 = Conversion.hexToShort("(nHexs-1)*4+dstPos is greater or equal to than 8", 1211, (short) (-933), 52, 0);
      assertEquals((short) (-933), short0);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToInt("[2 Qn7]FQfd:/.VtZI", (-2927), (-1446), 77, 2924);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      int int0 = Conversion.hexToInt((String) null, (-3980), 1530, 5232, (-3980));
      assertEquals(1530, int0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexToLong("", 3, 3, 3, 3);
        fail("Expecting exception: StringIndexOutOfBoundsException");
      
      } catch(StringIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      String string0 = "V>|9X|;Fo:\\5\\u{FaE";
      // Undeclared exception!
      try { 
        Conversion.hexToLong(string0, 2680, 0L, (-1507), 2680);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nHexs-1)*4+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      long long0 = Conversion.hexToLong((String) null, (short)0, (byte)0, 256, (short)0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      long long0 = Conversion.hexToLong("qdf@DY>%07|!P~e[?b", (-470), 82, (short)0, (-470));
      assertEquals(82L, long0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToShort(byteArray0, 9, (byte)0, (byte)32, 99);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 16
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-5862), (short) (-1548), (-5862), 0);
      assertEquals((short) (-1548), short0);
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      short short0 = Conversion.byteArrayToShort(byteArray0, 82, (short)0, (-470), (-470));
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      short short0 = Conversion.byteArrayToShort(byteArray0, (-3585), (byte)49, 678, (-3585));
      assertEquals((short)49, short0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      int int0 = Conversion.byteArrayToInt(byteArray0, (byte)0, (-1), 0, (-1));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      int int0 = Conversion.byteArrayToInt(byteArray0, 13, (byte)64, (-1796), (byte)0);
      assertEquals(64, int0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToInt(byteArray0, (-450), (-249), (-3949), 49);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -450
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      byte[] byteArray0 = new byte[4];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToLong(byteArray0, (byte)1, 4582L, 196, 97);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nBytes-1)*8+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      long long0 = Conversion.byteArrayToLong(byteArray0, (-741), 1L, 2763, (-741));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      // Undeclared exception!
      try { 
        Conversion.byteArrayToUuid(byteArray0, (-3639));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -3639
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      short[] shortArray0 = new short[7];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (short) (-3), (short)91, (-1404), 61);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -3
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      short[] shortArray0 = new short[7];
      int int0 = Conversion.shortArrayToInt(shortArray0, 4, 6211, 6211, (-452));
      assertEquals(6211, int0);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      short[] shortArray0 = new short[7];
      int int0 = Conversion.shortArrayToInt(shortArray0, (-1), 1572, (byte)4, (short)0);
      assertEquals(1572, int0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToInt(shortArray0, (short)0, (short)12, (short)9, (short)9);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 32
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      short[] shortArray0 = new short[6];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, 3928, 3928, 1, 3);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 3928
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      short[] shortArray0 = new short[5];
      long long0 = Conversion.shortArrayToLong(shortArray0, (-2499), (byte)1, 0, (short)0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      short[] shortArray0 = new short[0];
      long long0 = Conversion.shortArrayToLong(shortArray0, (byte)1, (-1L), (byte) (-127), (short) (-1524));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      short[] shortArray0 = new short[4];
      // Undeclared exception!
      try { 
        Conversion.shortArrayToLong(shortArray0, 55, (-1L), (short) (-1524), 1470);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nShorts-1)*16+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      int[] intArray0 = new int[4];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, (-1), (byte)1, (byte)1, (byte)1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      int[] intArray0 = new int[3];
      long long0 = Conversion.intArrayToLong(intArray0, 48, (-1L), (-2310), (-838));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      int[] intArray0 = new int[1];
      // Undeclared exception!
      try { 
        Conversion.intArrayToLong(intArray0, (-2043), 3047L, (-3132), 540);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // (nInts-1)*32+dstPos is greater or equal to than 64
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigitMsb0((-2030));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: -2030
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(8);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0((short)1);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      char char0 = Conversion.intToHexDigitMsb0(3);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.intToHexDigit((-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // nibble value not between 0 and 15: -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      char char0 = Conversion.intToHexDigit(10);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('1', char0);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      booleanArray0[4] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('7', char0);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('6', char0);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      booleanArray0[5] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
      assertEquals('8', char0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[2];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0, (-367));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
      }
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('9', char0);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[8];
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
      assertEquals('3', char0);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      booleanArray0[2] = true;
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, (-1));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -1
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[5];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0, 2558);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length-srcPos<4: src.length=5, srcPos=2558
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[1] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('2', char0);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('5', char0);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      booleanArray0[2] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('4', char0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[3];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 146);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 146
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[7];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('b', char0);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[0] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('d', char0);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[0] = true;
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('f', char0);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[1] = true;
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('e', char0);
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      booleanArray0[2] = true;
      booleanArray0[3] = true;
      char char0 = Conversion.binaryToHexDigit(booleanArray0);
      assertEquals('c', char0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[6];
      char char0 = Conversion.binaryToHexDigit(booleanArray0, (int) (short)0);
      assertEquals('0', char0);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigit(booleanArray0, 283);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('(');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '(' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('a');
      boolean[] booleanArray1 = Conversion.byteToBinary((byte)52, 732, booleanArray0, (-1), 0);
      assertSame(booleanArray0, booleanArray1);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('X');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'X' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('W');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'W' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('O');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'O' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('N');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'N' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('I');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'I' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('G');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'G' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('F');
      assertTrue(Arrays.equals(new boolean[] {true, true, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('A');
      assertTrue(Arrays.equals(new boolean[] {true, false, true, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('@');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '@' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('9');
      assertTrue(Arrays.equals(new boolean[] {true, false, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('5');
      assertTrue(Arrays.equals(new boolean[] {false, true, false, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('3');
      assertTrue(Arrays.equals(new boolean[] {false, false, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToBinary('Y');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Y' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitMsb0ToBinary('0');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('\"');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '\"' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('b');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('a');
      char char0 = Conversion.binaryToHexDigit(booleanArray0, 0);
      assertEquals('a', char0);
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('\\');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '\\' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('O');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'O' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('L');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'L' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('F');
      assertTrue(Arrays.equals(new boolean[] {true, true, true, true}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToBinary('<');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '<' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('6');
      // Undeclared exception!
      try { 
        Conversion.intToBinary((-2681), (-381), booleanArray0, (-585), 55);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // -585
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('4');
      // Undeclared exception!
      try { 
        Conversion.binaryToInt(booleanArray0, (short)1, (byte) (-127), (-430), 100);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('3');
      assertTrue(Arrays.equals(new boolean[] {true, true, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('1');
      assertTrue(Arrays.equals(new boolean[] {true, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('9');
      assertEquals(4, booleanArray0.length);
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      boolean[] booleanArray0 = Conversion.hexDigitToBinary('0');
      assertTrue(Arrays.equals(new boolean[] {false, false, false, false}, booleanArray0));
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('a');
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('[');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret '[' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('Q');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'Q' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('C');
      assertEquals(3, int0);
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt(';');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret ';' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      int int0 = Conversion.hexDigitMsb0ToInt('9');
      assertEquals(9, int0);
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitMsb0ToInt('g');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'g' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      // Undeclared exception!
      try { 
        Conversion.hexDigitToInt('y');
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot interpret 'y' as a hexadecimal digit
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      int int0 = Conversion.hexDigitToInt('5');
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[9];
      // Undeclared exception!
      try { 
        Conversion.binaryToHexDigitMsb0_4bits(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // src.length>8: src.length=9
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      boolean[] booleanArray0 = new boolean[0];
      // Undeclared exception!
      try { 
        Conversion.binaryBeMsb0ToHexDigit(booleanArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Cannot convert an empty array.
         //
         verifyException("org.apache.commons.lang3.Conversion", e);
      }
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      Conversion conversion0 = new Conversion();
  }
}
