/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:04:08 GMT 2025
 */

package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.PipedReader;
import java.io.PipedWriter;
import java.io.Reader;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class CSVRecord_ESTest extends CSVRecord_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test0()  throws Throwable  {
      PipedWriter pipedWriter0 = new PipedWriter();
      PipedReader pipedReader0 = new PipedReader(pipedWriter0);
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVParser cSVParser0 = CSVParser.parse((Reader) pipedReader0, cSVFormat0);
      String[] stringArray0 = new String[3];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "@Y(zff@k7jfUJ@,", (-998L), 1989L);
      boolean boolean0 = cSVRecord0.isMapped("o<i");
      assertEquals(3, cSVRecord0.size());
      assertFalse(boolean0);
      assertEquals((-998L), cSVRecord0.getRecordNumber());
      assertEquals(1989L, cSVRecord0.getCharacterPosition());
  }

  @Test(timeout = 4000)
  public void test1()  throws Throwable  {
      String[] stringArray0 = new String[2];
      CSVRecord cSVRecord0 = new CSVRecord((CSVParser) null, stringArray0, "", 2426L, 0L);
      // Undeclared exception!
      try { 
        cSVRecord0.get("");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // No header mapping was specified, the record values can't be accessed by name
         //
         verifyException("org.apache.commons.csv.CSVRecord", e);
      }
  }

  @Test(timeout = 4000)
  public void test2()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.MYSQL;
      PipedWriter pipedWriter0 = new PipedWriter();
      PipedReader pipedReader0 = new PipedReader(pipedWriter0, 4158);
      CSVParser cSVParser0 = cSVFormat0.parse(pipedReader0);
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, (String[]) null, "', recordNumber=", 4158, 4158);
      assertEquals(4158L, cSVRecord0.getCharacterPosition());
      assertEquals(4158L, cSVRecord0.getRecordNumber());
  }

  @Test(timeout = 4000)
  public void test3()  throws Throwable  {
      CSVFormat cSVFormat0 = CSVFormat.INFORMIX_UNLOAD_CSV;
      CSVParser cSVParser0 = CSVParser.parse("", cSVFormat0);
      String[] stringArray0 = new String[3];
      CSVRecord cSVRecord0 = new CSVRecord(cSVParser0, stringArray0, "", 0L, (-95L));
      String[] stringArray1 = cSVRecord0.values();
      assertEquals((-95L), cSVRecord0.getCharacterPosition());
      assertEquals(3, stringArray1.length);
      assertEquals(0L, cSVRecord0.getRecordNumber());
  }
}
