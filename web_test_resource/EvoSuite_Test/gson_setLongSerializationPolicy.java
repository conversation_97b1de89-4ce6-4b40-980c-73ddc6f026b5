/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 08:50:34 GMT 2025
 */

package com.google.gson;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.FieldNamingStrategy;
import com.google.gson.FormattingStyle;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.LongSerializationPolicy;
import com.google.gson.ReflectionAccessFilter;
import com.google.gson.Strictness;
import com.google.gson.ToNumberPolicy;
import com.google.gson.ToNumberStrategy;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.internal.Excluder;
import java.lang.reflect.Type;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class GsonBuilder_ESTest extends GsonBuilder_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setVersion(1228.48231098);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setObjectToNumberStrategy((ToNumberStrategy) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setFieldNamingStrategy((FieldNamingStrategy) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setExclusionStrategies((ExclusionStrategy[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setDateFormat("~F^}sDrRC");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // The date pattern '~F^}sDrRC' is not valid
         //
         verifyException("com.google.gson.GsonBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      // Undeclared exception!
      try { 
        gsonBuilder0.registerTypeAdapterFactory((TypeAdapterFactory) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      Object object0 = new Object();
      // Undeclared exception!
      try { 
        gsonBuilder0.registerTypeAdapter((Type) null, object0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.excludeFieldsWithModifiers((int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      // Undeclared exception!
      try { 
        gsonBuilder0.addSerializationExclusionStrategy((ExclusionStrategy) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.addDeserializationExclusionStrategy((ExclusionStrategy) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.Objects", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      GsonBuilder gsonBuilder0 = null;
      try {
        gsonBuilder0 = new GsonBuilder((Gson) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.gson.GsonBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      FieldNamingPolicy fieldNamingPolicy0 = FieldNamingPolicy.IDENTITY;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setFieldNamingStrategy(fieldNamingPolicy0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setFormattingStyle(formattingStyle0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      Strictness strictness0 = Strictness.STRICT;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setStrictness(strictness0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      Class<FormattingStyle> class0 = FormattingStyle.class;
      Object object0 = new Object();
      // Undeclared exception!
      try { 
        gsonBuilder0.registerTypeHierarchyAdapter(class0, object0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.gson.internal.$Gson$Preconditions", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      FormattingStyle formattingStyle0 = FormattingStyle.COMPACT;
      Class<Float> class0 = Float.TYPE;
      // Undeclared exception!
      try { 
        gsonBuilder0.registerTypeAdapter(class0, formattingStyle0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.gson.internal.$Gson$Preconditions", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setDateFormat(0, 1322);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid style: 1322
         //
         verifyException("com.google.gson.GsonBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setDateFormat((-454));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid style: -454
         //
         verifyException("com.google.gson.GsonBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setDateFormat("");
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      ExclusionStrategy[] exclusionStrategyArray0 = new ExclusionStrategy[8];
      GsonBuilder gsonBuilder1 = gsonBuilder0.setExclusionStrategies(exclusionStrategyArray0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      GsonBuilder gsonBuilder1 = gsonBuilder0.setVersion(0.0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      // Undeclared exception!
      try { 
        gsonBuilder0.setVersion((-1.0));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid version: -1.0
         //
         verifyException("com.google.gson.GsonBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      LongSerializationPolicy longSerializationPolicy0 = LongSerializationPolicy.DEFAULT;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setLongSerializationPolicy(longSerializationPolicy0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.disableInnerClassSerialization();
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      GsonBuilder gsonBuilder1 = gsonBuilder0.disableHtmlEscaping();
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      ExclusionStrategy exclusionStrategy0 = mock(ExclusionStrategy.class, new ViolatedAssumptionAnswer());
      GsonBuilder gsonBuilder1 = gsonBuilder0.addSerializationExclusionStrategy(exclusionStrategy0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      FieldNamingPolicy fieldNamingPolicy0 = FieldNamingPolicy.UPPER_CAMEL_CASE;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setFieldNamingPolicy(fieldNamingPolicy0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      int[] intArray0 = new int[0];
      GsonBuilder gsonBuilder1 = gsonBuilder0.excludeFieldsWithModifiers(intArray0);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      ReflectionAccessFilter reflectionAccessFilter0 = mock(ReflectionAccessFilter.class, new ViolatedAssumptionAnswer());
      GsonBuilder gsonBuilder1 = gsonBuilder0.addReflectionAccessFilter(reflectionAccessFilter0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setDateFormat(3);
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setDateFormat(0, 0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      ExclusionStrategy exclusionStrategy0 = mock(ExclusionStrategy.class, new ViolatedAssumptionAnswer());
      GsonBuilder gsonBuilder1 = gsonBuilder0.addDeserializationExclusionStrategy(exclusionStrategy0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      ToNumberPolicy toNumberPolicy0 = ToNumberPolicy.BIG_DECIMAL;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setObjectToNumberStrategy(toNumberPolicy0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      ToNumberStrategy toNumberStrategy0 = Gson.DEFAULT_NUMBER_TO_NUMBER_STRATEGY;
      GsonBuilder gsonBuilder1 = gsonBuilder0.setNumberToNumberStrategy(toNumberStrategy0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.generateNonExecutableJson();
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      Excluder excluder0 = Excluder.DEFAULT;
      GsonBuilder gsonBuilder1 = gsonBuilder0.registerTypeAdapterFactory(excluder0);
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Gson gson0 = new Gson();
      GsonBuilder gsonBuilder0 = new GsonBuilder(gson0);
      GsonBuilder gsonBuilder1 = gsonBuilder0.serializeSpecialFloatingPointValues();
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.serializeNulls();
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      Gson gson0 = gsonBuilder0.create();
      assertFalse(gson0.serializeNulls());
      assertTrue(gson0.htmlSafe());
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setLenient();
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.excludeFieldsWithoutExposeAnnotation();
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.setPrettyPrinting();
      assertSame(gsonBuilder1, gsonBuilder0);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.enableComplexMapKeySerialization();
      assertSame(gsonBuilder0, gsonBuilder1);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      GsonBuilder gsonBuilder0 = new GsonBuilder();
      GsonBuilder gsonBuilder1 = gsonBuilder0.disableJdkUnsafe();
      assertSame(gsonBuilder1, gsonBuilder0);
  }
}
