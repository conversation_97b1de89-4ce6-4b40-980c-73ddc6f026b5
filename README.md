# 测试用例可读性分析评估系统

这是一个用于评估不同测试用例生成方法产生的测试用例可读性和可用性的Web系统。系统提供了类似于JaCoCo或PiTest报告的界面风格，支持对测试用例进行详细的评分和分析。

## 功能特性

- 📊 **直观的评估界面**: 类似JaCoCo/PiTest的专业报告风格
- 🔍 **代码语法高亮**: 支持Java代码的语法高亮显示
- 📝 **多维度评分**: 支持命名直观性、代码布局、断言质量、迁移成本四个维度的评分
- 📈 **实时统计**: 显示评估进度和各工具的平均分数
- 💾 **数据持久化**: 评分数据自动保存到本地存储
- 📤 **结果导出**: 支持将评估结果导出为JSON格式
- 🎯 **评分标准**: 内置详细的评分标准说明

## 支持的测试生成工具

系统支持三种测试生成工具的匿名化评估：
- **方法 A**: 匿名化的测试生成方法
- **方法 B**: 匿名化的测试生成方法
- **方法 C**: 匿名化的测试生成方法

注：为确保评估的客观性，测试用例来源已匿名化处理，评估者无法知道具体的生成工具。

## 评分标准

### 可读性 (总分 2-6)

#### 命名直观性 (1-3分)
- **3分**: 大多数命名直观、易于理解
- **2分**: 部分命名直观、易于理解  
- **1分**: 大多数命名若无大量上下文难以理解

#### 代码布局 (1-3分)
- **3分**: 结构良好、逻辑清晰
- **2分**: 整体结构合理，仅有轻微问题
- **1分**: 逻辑混乱或存在大量冗余代码

### 可用性 (总分 2-6)

#### 断言质量 (1-3分)
- **3分**: 断言准确、完全契合测试场景
- **2分**: 断言合理但力度不足
- **1分**: 断言语义不正确

#### 迁移成本 (1-3分)
- **3分**: 无需任何修改即可直接采用
- **2分**: 仅需简单修改即可采用
- **1分**: 需大量优化方可采用

## 快速开始

### 方法一: 自动启动 (推荐)

```bash
# 生成数据并启动服务器
python3 start_server.py --generate

# 或者仅启动服务器 (如果已生成数据)
python3 start_server.py
```

### 方法二: 手动步骤

1. **生成数据文件**
   ```bash
   python3 generate_data.py
   ```

2. **启动HTTP服务器**
   ```bash
   python3 start_server.py
   ```

3. **访问评估系统**
   - 打开浏览器访问: http://localhost:8080
   - 系统会自动加载测试用例数据

## 目录结构

```
readable/
├── test_resource/              # 原始测试用例文件
│   ├── ChatUniTest_Test/      # 测试生成工具1的测试用例
│   ├── TestAgent_Test/        # 测试生成工具2的测试用例
│   └── EvoSuite_Test/         # 测试生成工具3的测试用例
├── index_with_data.html       # 主页面 (包含匿名化数据)
├── app_with_data.js          # JavaScript逻辑 (使用匿名化数据)
├── styles.css                # 样式文件
├── generate_data.py          # 数据生成和匿名化脚本
├── start_server.py           # 服务器启动脚本
└── README.md                 # 说明文档
```

## 使用说明

### 1. 导航操作
- 使用 **上一个/下一个** 按钮或键盘方向键切换测试方法
- 页面顶部显示当前进度和统计信息

### 2. 评分操作
- 每个测试用例下方有评分面板
- 点击对应分数按钮进行评分
- 评分会自动保存到本地存储
- 完成四个维度评分后显示总分

### 3. 查看评分标准
- 点击右下角的 **?** 按钮查看详细评分标准
- 标准包含每个维度的具体评分要求

### 4. 导出结果
- 点击 **导出结果** 按钮下载评估数据
- 导出文件为JSON格式，包含详细的评分信息和统计数据

### 5. 重置评分
- 点击 **重置评分** 按钮清除所有评分数据
- 操作不可撤销，请谨慎使用

## 技术特性

- **匿名化评估**: 测试用例来源完全匿名化，确保评估客观性
- **智能过滤**: 只保留所有工具都有的测试方法，确保公平比较
- **响应式设计**: 支持桌面和移动设备
- **本地存储**: 评分数据保存在浏览器本地存储中
- **增强语法高亮**: 使用Prism.js提供Java代码高亮，支持行号显示
- **现代UI**: 使用Font Awesome图标和现代CSS设计
- **无需数据库**: 纯前端实现，数据嵌入HTML中

## 系统要求

- Python 3.6+
- 现代浏览器 (Chrome, Firefox, Safari, Edge)
- 本地HTTP服务器环境

## 故障排除

### 端口被占用
```bash
# 使用其他端口
python3 start_server.py --port 8081
```

### 数据文件缺失
```bash
# 重新生成数据文件
python3 generate_data.py
```

### 浏览器缓存问题
- 按 Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac) 强制刷新
- 或在浏览器开发者工具中清除缓存

## 开发说明

如需修改或扩展系统功能:

1. **添加新的测试工具**: 在 `generate_data.py` 中的 `tools` 列表添加新工具目录名
2. **修改评分标准**: 编辑 `app.js` 中的 `createEvaluationItems` 方法
3. **调整界面样式**: 修改 `styles.css` 文件
4. **扩展导出功能**: 修改 `exportResults` 方法

## 许可证

本项目仅用于学术研究目的。

## 联系方式

如有问题或建议，请联系项目维护者。
