[{"id": "chart_compareTo", "project": "chart", "methodName": "compareTo", "fullName": "chart_compareTo"}, {"id": "chart_createWindPlot", "project": "chart", "methodName": "createWindPlot", "fullName": "chart_createWindPlot"}, {"id": "chart_getHeight", "project": "chart", "methodName": "getHeight", "fullName": "chart_getHeight"}, {"id": "chart_getLeft", "project": "chart", "methodName": "getLeft", "fullName": "chart_getLeft"}, {"id": "chart_getPaint", "project": "chart", "methodName": "<PERSON><PERSON><PERSON><PERSON>", "fullName": "chart_getPaint"}, {"id": "chart_getRadius", "project": "chart", "methodName": "getRadius", "fullName": "chart_getRadius"}, {"id": "chart_getType", "project": "chart", "methodName": "getType", "fullName": "chart_getType"}, {"id": "chart_getY", "project": "chart", "methodName": "getY", "fullName": "chart_getY"}, {"id": "chart_isBaseline", "project": "chart", "methodName": "isBaseline", "fullName": "chart_isBaseline"}, {"id": "chart_isLeft", "project": "chart", "methodName": "isLeft", "fullName": "chart_isLeft"}, {"id": "chart_isMultiple", "project": "chart", "methodName": "isMultiple", "fullName": "chart_isMultiple"}, {"id": "chart_isTopOrBottom", "project": "chart", "methodName": "isTopOrBottom", "fullName": "chart_isTopOrBottom"}, {"id": "chart_isVerticalCenter", "project": "chart", "methodName": "isVerticalCenter", "fullName": "chart_isVerticalCenter"}, {"id": "chart_javascriptEscape", "project": "chart", "methodName": "javascriptEscape", "fullName": "chart_javascriptEscape"}, {"id": "chart_overlaps", "project": "chart", "methodName": "overlaps", "fullName": "chart_overlaps"}, {"id": "chart_setPaint", "project": "chart", "methodName": "<PERSON><PERSON><PERSON><PERSON>", "fullName": "chart_setPaint"}, {"id": "chart_trimWidth", "project": "chart", "methodName": "trimWidth", "fullName": "chart_trimWidth"}, {"id": "cli_clone", "project": "cli", "methodName": "clone", "fullName": "cli_clone"}, {"id": "cli_createObject", "project": "cli", "methodName": "createObject", "fullName": "cli_createObject"}, {"id": "cli_createURL", "project": "cli", "methodName": "createURL", "fullName": "cli_createURL"}, {"id": "cli_getArgList", "project": "cli", "methodName": "getArgList", "fullName": "cli_getArgList"}, {"id": "cli_getDescription", "project": "cli", "methodName": "getDescription", "fullName": "cli_getDescription"}, {"id": "cli_getLeftPadding", "project": "cli", "methodName": "getLeftPadding", "fullName": "cli_getLeftPadding"}, {"id": "cli_getLongOptSeparator", "project": "cli", "methodName": "getLongOptSeparator", "fullName": "cli_getLongOptSeparator"}, {"id": "cli_getMissingOptions", "project": "cli", "methodName": "getMissingOptions", "fullName": "cli_getMissingOptions"}, {"id": "cli_getOptionComparator", "project": "cli", "methodName": "getOptionComparator", "fullName": "cli_getOptionComparator"}, {"id": "cli_getValueClass", "project": "cli", "methodName": "getValueClass", "fullName": "cli_getValueClass"}, {"id": "cli_getValueSeparator", "project": "cli", "methodName": "getValueSeparator", "fullName": "cli_getValueSeparator"}, {"id": "cli_getValues", "project": "cli", "methodName": "getV<PERSON>ues", "fullName": "cli_getValues"}, {"id": "cli_getWidth", "project": "cli", "methodName": "getWidth", "fullName": "cli_getWidth"}, {"id": "cli_hasArgName", "project": "cli", "methodName": "hasArgName", "fullName": "cli_hasArgName"}, {"id": "cli_hasLongOpt", "project": "cli", "methodName": "hasLongOpt", "fullName": "cli_hasLongOpt"}, {"id": "cli_hasValueSeparator", "project": "cli", "methodName": "hasValueSeparator", "fullName": "cli_hasValueSeparator"}, {"id": "cli_hashCode", "project": "cli", "methodName": "hashCode", "fullName": "cli_hashCode"}, {"id": "cli_isValueCode", "project": "cli", "methodName": "isValueCode", "fullName": "cli_isValueCode"}, {"id": "cli_iterator", "project": "cli", "methodName": "iterator", "fullName": "cli_iterator"}, {"id": "cli_setOptionComparator", "project": "cli", "methodName": "setOptionComparator", "fullName": "cli_setOptionComparator"}, {"id": "cli_withArgName", "project": "cli", "methodName": "withArgName", "fullName": "cli_withArgName"}, {"id": "csv_getCharacterPosition", "project": "csv", "methodName": "getCharacterPosition", "fullName": "csv_getCharacterPosition"}, {"id": "csv_getCommentMarker", "project": "csv", "methodName": "get<PERSON><PERSON><PERSON><PERSON>ark<PERSON>", "fullName": "csv_getCommentMarker"}, {"id": "csv_getDelimiter", "project": "csv", "methodName": "getDelimiter", "fullName": "csv_getDelimiter"}, {"id": "csv_getEscapeCharacter", "project": "csv", "methodName": "getEscapeCharacter", "fullName": "csv_getEscapeCharacter"}, {"id": "csv_getIgnoreHeaderCase", "project": "csv", "methodName": "getIgnoreHeaderCase", "fullName": "csv_getIgnoreHeaderCase"}, {"id": "csv_getNullString", "project": "csv", "methodName": "getNullString", "fullName": "csv_getNullString"}, {"id": "csv_getQuoteCharacter", "project": "csv", "methodName": "getQuoteCharacter", "fullName": "csv_getQuoteCharacter"}, {"id": "csv_getTrailerComment", "project": "csv", "methodName": "getTrailerComment", "fullName": "csv_getTrailerComment"}, {"id": "csv_getTrailingDelimiter", "project": "csv", "methodName": "getTrailingDelimiter", "fullName": "csv_getTrailingDelimiter"}, {"id": "csv_hasComment", "project": "csv", "methodName": "hasComment", "fullName": "csv_hasComment"}, {"id": "csv_hasHeaderComment", "project": "csv", "methodName": "hasHeaderComment", "fullName": "csv_hasHeaderComment"}, {"id": "csv_hasTrailerComment", "project": "csv", "methodName": "hasTrailerComment", "fullName": "csv_hasTrailerComment"}, {"id": "csv_isEscapeCharacterSet", "project": "csv", "methodName": "isEscapeCharacterSet", "fullName": "csv_isEscapeCharacterSet"}, {"id": "csv_isNullStringSet", "project": "csv", "methodName": "isNullStringSet", "fullName": "csv_isNullStringSet"}, {"id": "csv_isQuoteCharacterSet", "project": "csv", "methodName": "isQuoteCharacterSet", "fullName": "csv_isQuoteCharacterSet"}, {"id": "csv_newFormat", "project": "csv", "methodName": "newFormat", "fullName": "csv_newFormat"}, {"id": "csv_toList", "project": "csv", "methodName": "toList", "fullName": "csv_toList"}, {"id": "csv_valueOf", "project": "csv", "methodName": "valueOf", "fullName": "csv_valueOf"}, {"id": "gson_disableHtmlEscaping", "project": "gson", "methodName": "disableHtmlEscaping", "fullName": "gson_disableHtmlEscaping"}, {"id": "gson_disableJdkUnsafe", "project": "gson", "methodName": "disableJdkUnsafe", "fullName": "gson_disableJdkUnsafe"}, {"id": "gson_getAccessibleObjectDescription", "project": "gson", "methodName": "getAccessibleObjectDescription", "fullName": "gson_getAccessibleObjectDescription"}, {"id": "gson_getType", "project": "gson", "methodName": "getType", "fullName": "gson_getType"}, {"id": "gson_isJavaType", "project": "gson", "methodName": "isJavaType", "fullName": "gson_isJavaType"}, {"id": "gson_isPrimitive", "project": "gson", "methodName": "isPrimitive", "fullName": "gson_isPrimitive"}, {"id": "gson_isString", "project": "gson", "methodName": "isString", "fullName": "gson_isString"}, {"id": "gson_setFieldNamingPolicy", "project": "gson", "methodName": "setFieldNamingPolicy", "fullName": "gson_setFieldNamingPolicy"}, {"id": "gson_unwrap", "project": "gson", "methodName": "unwrap", "fullName": "gson_unwrap"}, {"id": "gson_withExclusionStrategy", "project": "gson", "methodName": "withExclusionStrategy", "fullName": "gson_withExclusionStrategy"}, {"id": "lang_availableLocaleSet", "project": "lang", "methodName": "availableLocaleSet", "fullName": "lang_availableLocaleSet"}, {"id": "lang_binaryToByte", "project": "lang", "methodName": "binaryToByte", "fullName": "lang_binaryToByte"}, {"id": "lang_booleanValue", "project": "lang", "methodName": "booleanValue", "fullName": "lang_booleanValue"}, {"id": "lang_byteToBinary", "project": "lang", "methodName": "byteToBinary", "fullName": "lang_byteToBinary"}, {"id": "lang_byteToHex", "project": "lang", "methodName": "byteToHex", "fullName": "lang_byteToHex"}, {"id": "lang_byteValue", "project": "lang", "methodName": "byteValue", "fullName": "lang_byteValue"}, {"id": "lang_getHostName", "project": "lang", "methodName": "getHostName", "fullName": "lang_getHostName"}, {"id": "lang_hexDigitMsb0ToInt", "project": "lang", "methodName": "hexDigitMsb0ToInt", "fullName": "lang_hexDigitMsb0ToInt"}, {"id": "lang_hexDigitToBinary", "project": "lang", "methodName": "hexDigitToBinary", "fullName": "lang_hexDigitToBinary"}, {"id": "lang_intArrayToLong", "project": "lang", "methodName": "intArrayToLong", "fullName": "lang_intArrayToLong"}, {"id": "lang_intToBinary", "project": "lang", "methodName": "intToBinary", "fullName": "lang_intToBinary"}, {"id": "lang_intToHex", "project": "lang", "methodName": "intToHex", "fullName": "lang_intToHex"}, {"id": "lang_intToHexDigit", "project": "lang", "methodName": "intToHexDigit", "fullName": "lang_intToHexDigit"}, {"id": "lang_intToHexDigitMsb0", "project": "lang", "methodName": "intToHexDigitMsb0", "fullName": "lang_intToHexDigitMsb0"}, {"id": "lang_isJavaVersionAtLeast", "project": "lang", "methodName": "isJavaVersionAtLeast", "fullName": "lang_isJavaVersionAtLeast"}, {"id": "lang_isTrue", "project": "lang", "methodName": "isTrue", "fullName": "lang_isTrue"}, {"id": "lang_longToHex", "project": "lang", "methodName": "longToHex", "fullName": "lang_longToHex"}, {"id": "lang_setTrue", "project": "lang", "methodName": "setTrue", "fullName": "lang_setTrue"}, {"id": "lang_toBoolean", "project": "lang", "methodName": "toBoolean", "fullName": "lang_toBoolean"}, {"id": "ruler_absencePatterns", "project": "ruler", "methodName": "absencePatterns", "fullName": "ruler_absencePatterns"}, {"id": "ruler_entries", "project": "ruler", "methodName": "entries", "fullName": "ruler_entries"}, {"id": "ruler_existencePatterns", "project": "ruler", "methodName": "existencePatterns", "fullName": "ruler_existencePatterns"}, {"id": "ruler_findPattern", "project": "ruler", "methodName": "findPattern", "fullName": "ruler_findPattern"}, {"id": "ruler_getBytes", "project": "ruler", "methodName": "getBytes", "fullName": "ruler_getBytes"}, {"id": "ruler_getId", "project": "ruler", "methodName": "getId", "fullName": "ruler_getId"}, {"id": "ruler_getNameState", "project": "ruler", "methodName": "getNameState", "fullName": "ruler_getNameState"}, {"id": "ruler_getNextState", "project": "ruler", "methodName": "getNextState", "fullName": "ruler_getNextState"}, {"id": "ruler_getParser", "project": "ruler", "methodName": "<PERSON><PERSON><PERSON><PERSON>", "fullName": "ruler_getParser"}, {"id": "ruler_getPattern", "project": "ruler", "methodName": "getPattern", "fullName": "ruler_getPattern"}, {"id": "ruler_is", "project": "ruler", "methodName": "is", "fullName": "ruler_is"}, {"id": "ruler_is<PERSON><PERSON><PERSON><PERSON><PERSON>", "project": "ruler", "methodName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullName": "ruler_is<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": "ruler_prefixMatch", "project": "ruler", "methodName": "prefixMatch", "fullName": "ruler_prefixMatch"}, {"id": "ruler_singular", "project": "ruler", "methodName": "singular", "fullName": "ruler_singular"}]