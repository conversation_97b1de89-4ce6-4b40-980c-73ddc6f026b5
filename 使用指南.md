# 测试用例可读性分析评估系统 - 使用指南

## 🚀 快速启动

### 一键启动 (推荐)
```bash
python3 run_evaluation_system.py
```

这个命令会自动：
- 检查系统要求
- 生成必要的数据文件  
- 启动HTTP服务器
- 打开浏览器访问评估系统

### 手动启动
如果需要更多控制，可以分步执行：

1. **生成数据文件**
   ```bash
   python3 generate_data.py
   ```

2. **启动服务器**
   ```bash
   python3 start_server.py
   ```

## 📋 系统要求

- Python 3.6 或更高版本
- 现代浏览器 (Chrome, Firefox, Safari, Edge)
- test_resource 目录包含测试用例文件

## 📁 目录结构说明

确保你的目录结构如下：
```
readable/
├── test_resource/
│   ├── ChatUniTest_Test/     # 测试生成工具1的测试用例
│   │   ├── chart_compareTo.java
│   │   ├── chart_getHeight.java
│   │   └── ...
│   ├── TestAgent_Test/       # 测试生成工具2的测试用例
│   │   ├── chart_compareTo.java
│   │   ├── chart_getHeight.java
│   │   └── ...
│   └── EvoSuite_Test/        # 测试生成工具3的测试用例
│       ├── chart_compareTo.java
│       ├── chart_getHeight.java
│       └── ...
├── run_evaluation_system.py  # 一键启动脚本
└── 其他系统文件...
```

## 🎯 评估流程

### 1. 启动系统
运行 `python3 run_evaluation_system.py`，系统会自动打开浏览器

### 2. 导航操作
- 使用 **上一个/下一个** 按钮切换测试方法
- 或使用键盘左右方向键快速导航
- 页面顶部显示当前进度 (如: 1/128)

### 3. 查看测试用例
每个页面显示同一个被测函数的所有测试用例（匿名化显示）：
- **方法 A**: 蓝色标签
- **方法 B**: 紫色标签
- **方法 C**: 绿色标签

注：为确保评估客观性，测试用例来源已完全匿名化

### 4. 进行评分
为每个测试用例的四个维度评分：

#### 可读性维度
- **命名直观性** (1-3分): 变量名和方法名是否清晰易懂
- **代码布局** (1-3分): 代码结构和格式是否良好

#### 可用性维度  
- **断言质量** (1-3分): 断言是否准确有效
- **迁移成本** (1-3分): 是否容易集成到实际项目

### 5. 查看评分标准
点击右下角的 **?** 按钮查看详细的评分标准说明

### 6. 保存和导出
- 评分会自动保存到浏览器本地存储
- 点击 **导出结果** 按钮下载完整的评估数据
- 导出文件为JSON格式，包含详细统计信息

## 📊 评分标准详解

### 命名直观性 (1-3分)
- **3分**: 大多数变量名、方法名直观易懂，无需额外解释
- **2分**: 部分命名清晰，但有些需要结合上下文理解  
- **1分**: 大多数命名模糊，需要大量上下文才能理解

### 代码布局 (1-3分)
- **3分**: 代码结构清晰，逻辑分明，格式规范
- **2分**: 整体结构合理，仅有轻微的格式或逻辑问题
- **1分**: 代码混乱，逻辑不清，或存在大量冗余

### 断言质量 (1-3分)
- **3分**: 断言准确、全面，完全验证了测试场景
- **2分**: 断言基本正确，但覆盖不够全面
- **1分**: 断言错误或无法有效验证测试目标

### 迁移成本 (1-3分)
- **3分**: 可以直接复制到项目中使用，无需修改
- **2分**: 需要少量修改(如导入包、调整参数)即可使用
- **1分**: 需要大量修改和优化才能在实际项目中使用

## 🔧 常见问题

### Q: 端口被占用怎么办？
A: 系统会自动尝试8080-8084端口，或手动指定：
```bash
python3 start_server.py --port 8081
```

### Q: 浏览器显示空白页面？
A: 请确保：
1. 使用HTTP服务器访问，不要直接打开HTML文件
2. 清除浏览器缓存后刷新
3. 检查浏览器控制台是否有错误信息

### Q: 找不到测试用例？
A: 请确保：
1. test_resource目录存在
2. 目录中包含ChatUniTest_Test或TestAgent_Test子目录
3. 子目录中有.java文件

### Q: 评分数据丢失？
A: 评分数据保存在浏览器本地存储中：
- 不要清除浏览器数据
- 定期使用"导出结果"功能备份
- 同一浏览器同一域名下数据会保持

### Q: 如何添加新的测试工具？
A: 
1. 在test_resource目录下创建新的工具目录
2. 修改generate_data.py中的tools列表
3. 重新运行数据生成脚本

## 📈 使用建议

1. **分批评估**: 建议每次评估20-30个方法，避免疲劳
2. **保持一致**: 在开始前仔细阅读评分标准，保持评分一致性
3. **定期备份**: 每完成一批评估后导出结果备份
4. **多人协作**: 可以多人分别评估，最后合并结果
5. **记录笔记**: 对特殊情况可以在导出的JSON中添加备注

## 🆘 技术支持

如遇到技术问题：
1. 检查Python版本是否符合要求
2. 确认目录结构是否正确
3. 查看终端错误信息
4. 尝试重新生成数据文件

---

**祝您评估顺利！** 🎉
