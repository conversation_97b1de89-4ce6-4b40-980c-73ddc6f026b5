<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提交功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 6px; }
        .test-section h3 { margin-top: 0; color: #007bff; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 提交功能测试页面</h1>
        
        <div class="test-section">
            <h3>🔧 测试场景</h3>
            <button class="btn-primary" onclick="testEmptySubmission()">测试空提交</button>
            <button class="btn-warning" onclick="testPartialSubmission()">测试部分评分提交</button>
            <button class="btn-success" onclick="testCompleteSubmission()">测试完整评分提交</button>
            <button class="btn-danger" onclick="clearData()">清除所有数据</button>
        </div>

        <div class="test-section">
            <h3>📋 当前状态</h3>
            <div id="currentStatus"></div>
            <button class="btn-primary" onclick="checkStatus()">检查状态</button>
        </div>

        <div class="test-section">
            <h3>🚀 提交测试</h3>
            <button class="btn-success" onclick="testSubmission()">执行提交测试</button>
            <div id="submissionResult"></div>
        </div>

        <div class="test-section">
            <h3>📝 日志</h3>
            <div id="logOutput"></div>
            <button class="btn-primary" onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <script>
        // 模拟评估应用的核心功能
        class TestEvaluationApp {
            constructor() {
                this.evaluationData = JSON.parse(localStorage.getItem('evaluationData') || '{}');
                this.methodToolMappings = JSON.parse(localStorage.getItem('methodToolMappings') || '{}');
                this.testMethods = [
                    { name: 'chart_compareTo' },
                    { name: 'chart_createWindPlot' },
                    { name: 'chart_getHeight' }
                ];
            }

            // 检查未评分的方法
            checkUnevaluatedMethods() {
                const unevaluatedMethods = [];
                
                for (const method of this.testMethods) {
                    const methodData = this.evaluationData[method.name];
                    if (!methodData) {
                        unevaluatedMethods.push(method.name);
                        continue;
                    }
                    
                    const toolMapping = this.methodToolMappings[method.name];
                    if (!toolMapping) {
                        unevaluatedMethods.push(method.name);
                        continue;
                    }
                    
                    let hasIncompleteEvaluation = false;
                    for (const toolId in toolMapping) {
                        const toolData = methodData[toolId];
                        if (!toolData || Object.keys(toolData).length === 0) {
                            hasIncompleteEvaluation = true;
                            break;
                        }
                        
                        const requiredCriteria = ['naming', 'layout', 'assertion', 'migration'];
                        for (const criterion of requiredCriteria) {
                            if (!toolData[criterion] || toolData[criterion] === 0) {
                                hasIncompleteEvaluation = true;
                                break;
                            }
                        }
                        if (hasIncompleteEvaluation) break;
                    }
                    
                    if (hasIncompleteEvaluation) {
                        unevaluatedMethods.push(method.name);
                    }
                }
                
                return unevaluatedMethods;
            }

            // 提交评分
            async submitEvaluation() {
                if (Object.keys(this.evaluationData).length === 0) {
                    throw new Error('请先完成一些评分再提交！');
                }

                const unevaluatedMethods = this.checkUnevaluatedMethods();
                if (unevaluatedMethods.length > 0) {
                    let message = `还有 ${unevaluatedMethods.length} 个方法未完成评分：\n\n`;
                    
                    if (unevaluatedMethods.length <= 3) {
                        message += unevaluatedMethods.map(name => `• ${name}`).join('\n');
                    } else {
                        message += unevaluatedMethods.slice(0, 3).map(name => `• ${name}`).join('\n');
                        message += `\n... 还有 ${unevaluatedMethods.length - 3} 个方法`;
                    }
                    
                    message += '\n\n是否继续提交已完成的评分？';
                    
                    if (!confirm(message)) {
                        throw new Error('用户取消提交');
                    }
                }

                const submissionData = {
                    evaluationData: this.evaluationData,
                    methodToolMappings: this.methodToolMappings,
                    metadata: {
                        totalMethods: this.testMethods.length,
                        evaluatedMethods: Object.keys(this.evaluationData).length,
                        unevaluatedMethods: unevaluatedMethods.length,
                        timestamp: new Date().toISOString(),
                        userAgent: navigator.userAgent
                    }
                };

                const response = await fetch('/api/submit-evaluation', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(submissionData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error || '提交失败');
                }

                return result;
            }
        }

        const app = new TestEvaluationApp();

        function log(message) {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logOutput').innerHTML = '';
        }

        function testEmptySubmission() {
            app.evaluationData = {};
            app.methodToolMappings = {};
            localStorage.setItem('evaluationData', '{}');
            localStorage.setItem('methodToolMappings', '{}');
            log('✅ 设置为空提交状态');
            checkStatus();
        }

        function testPartialSubmission() {
            app.evaluationData = {
                'chart_compareTo': {
                    'Tool_1': { naming: 3, layout: 2, assertion: 3, migration: 2 }
                }
            };
            app.methodToolMappings = {
                'chart_compareTo': { 'Tool_1': 'Method_A', 'Tool_2': 'Method_B' }
            };
            localStorage.setItem('evaluationData', JSON.stringify(app.evaluationData));
            localStorage.setItem('methodToolMappings', JSON.stringify(app.methodToolMappings));
            log('✅ 设置为部分评分状态（只有1个方法部分完成）');
            checkStatus();
        }

        function testCompleteSubmission() {
            app.evaluationData = {
                'chart_compareTo': {
                    'Tool_1': { naming: 3, layout: 2, assertion: 3, migration: 2 },
                    'Tool_2': { naming: 2, layout: 3, assertion: 2, migration: 3 }
                },
                'chart_createWindPlot': {
                    'Tool_1': { naming: 1, layout: 1, assertion: 1, migration: 1 },
                    'Tool_2': { naming: 2, layout: 2, assertion: 2, migration: 2 }
                },
                'chart_getHeight': {
                    'Tool_1': { naming: 3, layout: 3, assertion: 3, migration: 3 },
                    'Tool_2': { naming: 1, layout: 2, assertion: 1, migration: 2 }
                }
            };
            app.methodToolMappings = {
                'chart_compareTo': { 'Tool_1': 'Method_A', 'Tool_2': 'Method_B' },
                'chart_createWindPlot': { 'Tool_1': 'Method_C', 'Tool_2': 'Method_D' },
                'chart_getHeight': { 'Tool_1': 'Method_A', 'Tool_2': 'Method_C' }
            };
            localStorage.setItem('evaluationData', JSON.stringify(app.evaluationData));
            localStorage.setItem('methodToolMappings', JSON.stringify(app.methodToolMappings));
            log('✅ 设置为完整评分状态（所有方法都已完成）');
            checkStatus();
        }

        function clearData() {
            localStorage.removeItem('evaluationData');
            localStorage.removeItem('methodToolMappings');
            app.evaluationData = {};
            app.methodToolMappings = {};
            log('🗑️ 已清除所有数据');
            checkStatus();
        }

        function checkStatus() {
            const unevaluated = app.checkUnevaluatedMethods();
            const total = app.testMethods.length;
            const evaluated = total - unevaluated.length;
            const completion = ((evaluated / total) * 100).toFixed(1);

            const statusDiv = document.getElementById('currentStatus');
            statusDiv.innerHTML = `
                <div class="status info">
                    <strong>评分状态:</strong><br>
                    总方法数: ${total}<br>
                    已完成: ${evaluated}<br>
                    未完成: ${unevaluated.length}<br>
                    完成度: ${completion}%<br>
                    ${unevaluated.length > 0 ? `<br><strong>未完成方法:</strong><br>${unevaluated.join(', ')}` : ''}
                </div>
            `;
            log(`📊 状态检查: ${evaluated}/${total} 完成 (${completion}%)`);
        }

        async function testSubmission() {
            const resultDiv = document.getElementById('submissionResult');
            resultDiv.innerHTML = '<div class="status info">提交中...</div>';
            
            try {
                const result = await app.submitEvaluation();
                resultDiv.innerHTML = `
                    <div class="status success">
                        <strong>✅ 提交成功!</strong><br>
                        提交ID: ${result.submissionId}<br>
                        时间: ${new Date(result.timestamp).toLocaleString()}<br>
                        消息: ${result.message}
                    </div>
                `;
                log(`✅ 提交成功: ${result.submissionId}`);
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="status error">
                        <strong>❌ 提交失败!</strong><br>
                        错误: ${error.message}
                    </div>
                `;
                log(`❌ 提交失败: ${error.message}`);
            }
        }

        // 初始化
        checkStatus();
        log('🚀 测试页面已加载');
    </script>
</body>
</html>
