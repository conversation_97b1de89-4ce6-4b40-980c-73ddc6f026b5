<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具映射测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .mapping { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        pre { background: #f0f0f0; padding: 10px; overflow-x: auto; }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        // 从主页面复制数据
        window.TEST_METHODS_DATA = [{"id": "chart_compareTo", "project": "chart", "methodName": "compareTo", "fullName": "chart_compareTo"}, {"id": "chart_createWindPlot", "project": "chart", "methodName": "createWindPlot", "fullName": "chart_createWindPlot"}, {"id": "chart_getHeight", "project": "chart", "methodName": "getHeight", "fullName": "chart_getHeight"}];
        
        window.TEST_CASES_DATA = {"Method_A": {"chart_compareTo": "test content A1", "chart_createWindPlot": "test content A2", "chart_getHeight": "test content A3"}};
        
        // 模拟工具映射逻辑
        function shuffleArray(array) {
            const shuffled = [...array];
            for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
            }
            return shuffled;
        }
        
        function testToolMapping() {
            const output = document.getElementById('output');
            let html = '<h2>工具映射测试结果:</h2>';
            
            const methodToolMappings = {};
            
            // 测试前3个方法
            const testMethods = window.TEST_METHODS_DATA.slice(0, 3);
            
            testMethods.forEach(method => {
                html += `<div class="test-section">`;
                html += `<h3>方法: ${method.id}</h3>`;
                
                // 获取可用工具
                const methodData = window.TEST_CASES_DATA;
                const toolNames = Object.keys(methodData);
                html += `<p>原始工具: ${toolNames.join(', ')}</p>`;
                
                // 随机打乱
                const shuffledToolNames = shuffleArray([...toolNames]);
                html += `<p>打乱后顺序: ${shuffledToolNames.join(', ')}</p>`;
                
                // 创建映射
                methodToolMappings[method.id] = {};
                const mappingInfo = [];
                
                shuffledToolNames.forEach((originalToolName, index) => {
                    const anonymousName = `Tool_${index + 1}`;
                    const displayName = `方法 ${String.fromCharCode(65 + index)}`;
                    
                    methodToolMappings[method.id][anonymousName] = originalToolName;
                    mappingInfo.push(`${displayName} (${anonymousName}) → ${originalToolName}`);
                });
                
                html += `<div class="mapping">`;
                html += `<strong>映射关系:</strong><br>`;
                html += mappingInfo.join('<br>');
                html += `</div>`;
                
                html += `</div>`;
            });
            
            html += `<h3>完整映射数据结构:</h3>`;
            html += `<pre>${JSON.stringify(methodToolMappings, null, 2)}</pre>`;
            
            // 模拟评分统计
            html += `<h3>评分统计测试:</h3>`;
            const mockEvaluationData = {
                "chart_compareTo": {
                    "Tool_1": {"readability": 4, "maintainability": 3, "completeness": 5, "correctness": 4}
                },
                "chart_createWindPlot": {
                    "Tool_1": {"readability": 3, "maintainability": 4, "completeness": 4, "correctness": 5}
                }
            };
            
            html += `<p>模拟评分数据:</p>`;
            html += `<pre>${JSON.stringify(mockEvaluationData, null, 2)}</pre>`;
            
            // 按原始工具统计
            const originalToolStats = {};
            Object.keys(mockEvaluationData).forEach(methodId => {
                const methodData = mockEvaluationData[methodId];
                const toolMapping = methodToolMappings[methodId] || {};
                
                Object.keys(toolMapping).forEach(anonymousTool => {
                    const originalToolName = toolMapping[anonymousTool];
                    const toolData = methodData[anonymousTool];
                    
                    if (toolData) {
                        if (!originalToolStats[originalToolName]) {
                            originalToolStats[originalToolName] = [];
                        }
                        
                        const toolScores = Object.values(toolData);
                        const totalScore = toolScores.reduce((sum, score) => sum + score, 0);
                        originalToolStats[originalToolName].push(totalScore);
                    }
                });
            });
            
            html += `<p>按原始工具统计:</p>`;
            html += `<pre>${JSON.stringify(originalToolStats, null, 2)}</pre>`;
            
            output.innerHTML = html;
        }
    </script>
</head>
<body>
    <h1>工具映射逻辑测试</h1>
    <p>这个页面用于测试匿名工具映射和评分统计逻辑是否正确。</p>
    
    <button onclick="testToolMapping()">运行测试</button>
    <button onclick="testToolMapping()">重新测试（验证随机性）</button>
    
    <div id="output"></div>
</body>
</html>
