<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试评估状态计算</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .result { background: #e8f5e9; padding: 10px; margin: 10px 0; border-radius: 5px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试评估状态计算</h1>
        
        <div class="test-section">
            <h3>测试场景</h3>
            <p>模拟以下评分状态：</p>
            <ul>
                <li>完全完成的方法：2个（所有工具都有完整评分）</li>
                <li>部分完成的方法：1个（只有部分工具有评分）</li>
                <li>未开始的方法：96个</li>
            </ul>
            <button onclick="runTest()">运行测试</button>
        </div>
        
        <div id="results" class="result" style="display: none;">
            <h3>测试结果</h3>
            <pre id="output"></pre>
        </div>
    </div>

    <script>
        // 模拟测试方法数据（简化版）
        const testMethods = [
            {id: "method1", name: "method1"},
            {id: "method2", name: "method2"}, 
            {id: "method3", name: "method3"}
        ];
        
        // 模拟评分数据
        const evaluationData = {
            "method1": {
                "Tool_1": {"naming": 1, "layout": 1, "assertion": 1, "migration": 1},
                "Tool_2": {"naming": 2, "layout": 2, "assertion": 2, "migration": 2},
                "Tool_3": {"naming": 3, "layout": 3, "assertion": 3, "migration": 3},
                "Tool_4": {"naming": 1, "layout": 1, "assertion": 1, "migration": 1}
            },
            "method2": {
                "Tool_1": {"naming": 2, "layout": 2, "assertion": 2, "migration": 2},
                "Tool_2": {"naming": 1, "layout": 1, "assertion": 1, "migration": 1},
                "Tool_3": {"naming": 3, "layout": 3, "assertion": 3, "migration": 3},
                "Tool_4": {"naming": 2, "layout": 2, "assertion": 2, "migration": 2}
            },
            "method3": {
                "Tool_1": {"naming": 1, "layout": 1, "assertion": 1, "migration": 1}
                // 注意：Tool_2, Tool_3, Tool_4 缺失，这应该被识别为部分完成
            }
        };
        
        // 模拟工具映射
        const methodToolMappings = {
            "method1": {"Tool_1": "Method_A", "Tool_2": "Method_B", "Tool_3": "Method_C", "Tool_4": "Method_D"},
            "method2": {"Tool_1": "Method_B", "Tool_2": "Method_A", "Tool_3": "Method_D", "Tool_4": "Method_C"},
            "method3": {"Tool_1": "Method_C", "Tool_2": "Method_D", "Tool_3": "Method_A", "Tool_4": "Method_B"}
        };

        // 复制修复后的评估状态函数
        function getEvaluationStatus() {
            const notStarted = [];      // 未开始评分的方法
            const incomplete = [];      // 部分完成的方法
            const completed = [];       // 完全完成的方法
            
            for (const method of testMethods) {
                const methodData = evaluationData[method.id];
                if (!methodData) {
                    notStarted.push(method.id);
                    continue;
                }

                // 检查该方法的所有工具是否都已评分
                const toolMapping = methodToolMappings[method.id];
                if (!toolMapping) {
                    notStarted.push(method.id);
                    continue;
                }

                let hasIncompleteEvaluation = false;
                let hasAnyEvaluation = false;

                for (const toolId in toolMapping) {
                    const toolData = methodData[toolId];
                    if (!toolData || Object.keys(toolData).length === 0) {
                        hasIncompleteEvaluation = true;
                        continue;
                    }

                    hasAnyEvaluation = true;

                    // 检查是否所有评分维度都已完成
                    const requiredCriteria = ['naming', 'layout', 'assertion', 'migration'];
                    for (const criterion of requiredCriteria) {
                        if (!toolData[criterion] || toolData[criterion] === 0) {
                            hasIncompleteEvaluation = true;
                            break;
                        }
                    }
                    // 修复：移除了这里的 break
                }

                if (!hasAnyEvaluation) {
                    notStarted.push(method.id);
                } else if (hasIncompleteEvaluation) {
                    incomplete.push(method.id);
                } else {
                    completed.push(method.id);
                }
            }

            return {
                notStarted,
                incomplete, 
                completed,
                totalUnevaluated: notStarted.length + incomplete.length
            };
        }

        function runTest() {
            const status = getEvaluationStatus();
            
            const output = `
评估状态计算结果：
===================

已完成方法 (${status.completed.length}): ${status.completed.join(', ')}
部分完成方法 (${status.incomplete.length}): ${status.incomplete.join(', ')}
未开始方法 (${status.notStarted.length}): ${status.notStarted.join(', ')}

总计未完成: ${status.totalUnevaluated}

预期结果：
- 已完成: 2个 (method1, method2)
- 部分完成: 1个 (method3)
- 未开始: 0个

实际结果：
- 已完成: ${status.completed.length}个
- 部分完成: ${status.incomplete.length}个  
- 未开始: ${status.notStarted.length}个

测试${status.completed.length === 2 && status.incomplete.length === 1 && status.notStarted.length === 0 ? '通过' : '失败'}！
            `;
            
            document.getElementById('output').textContent = output;
            document.getElementById('results').style.display = 'block';
        }
    </script>
</body>
</html>
