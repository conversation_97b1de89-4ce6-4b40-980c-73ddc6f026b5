<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 6px; }
        .debug-section h3 { margin-top: 0; color: #007bff; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 状态调试页面</h1>
        
        <div class="debug-section">
            <h3>📊 当前数据状态</h3>
            <button onclick="loadCurrentData()">加载当前数据</button>
            <button onclick="debugEvaluationStatus()">调试评分状态</button>
            <div id="currentDataOutput"></div>
        </div>

        <div class="debug-section">
            <h3>🧪 测试评分状态函数</h3>
            <button onclick="testStatusFunction()">测试状态函数</button>
            <div id="statusTestOutput"></div>
        </div>

        <div class="debug-section">
            <h3>📝 详细日志</h3>
            <div id="debugLog"></div>
            <button onclick="clearDebugLog()">清除日志</button>
        </div>
    </div>

    <script>
        // 复制主应用的核心逻辑
        class DebugApp {
            constructor() {
                this.evaluationData = JSON.parse(localStorage.getItem('evaluationData') || '{}');
                this.methodToolMappings = JSON.parse(localStorage.getItem('methodToolMappings') || '{}');
                // 简化的测试方法列表
                this.testMethods = [
                    { name: 'chart_compareTo' },
                    { name: 'chart_createWindPlot' },
                    { name: 'chart_getHeight' },
                    { name: 'chart_getLeft' },
                    { name: 'chart_getPaint' },
                    { name: 'chart_getRadius' },
                    { name: 'chart_isLeft' }
                ];
            }

            // 检查评分状态
            getEvaluationStatus() {
                const notStarted = [];      // 未开始评分的方法
                const incomplete = [];      // 部分完成的方法
                const completed = [];       // 完全完成的方法
                
                debugLog(`开始检查 ${this.testMethods.length} 个方法的评分状态...`);
                
                for (const method of this.testMethods) {
                    debugLog(`\n检查方法: ${method.name}`);
                    
                    const methodData = this.evaluationData[method.name];
                    if (!methodData) {
                        debugLog(`  - 没有评分数据，标记为未开始`);
                        notStarted.push(method.name);
                        continue;
                    }

                    // 检查该方法的所有工具是否都已评分
                    const toolMapping = this.methodToolMappings[method.name];
                    if (!toolMapping) {
                        debugLog(`  - 没有工具映射，标记为未开始`);
                        notStarted.push(method.name);
                        continue;
                    }

                    debugLog(`  - 工具映射: ${JSON.stringify(toolMapping)}`);
                    debugLog(`  - 评分数据: ${JSON.stringify(methodData)}`);

                    let hasIncompleteEvaluation = false;
                    let hasAnyEvaluation = false;

                    for (const toolId in toolMapping) {
                        debugLog(`    检查工具: ${toolId}`);
                        const toolData = methodData[toolId];
                        if (!toolData || Object.keys(toolData).length === 0) {
                            debugLog(`      - 工具 ${toolId} 没有评分数据`);
                            hasIncompleteEvaluation = true;
                            continue;
                        }

                        hasAnyEvaluation = true;
                        debugLog(`      - 工具 ${toolId} 有评分数据: ${JSON.stringify(toolData)}`);

                        // 检查是否所有评分维度都已完成
                        const requiredCriteria = ['naming', 'layout', 'assertion', 'migration'];
                        for (const criterion of requiredCriteria) {
                            if (!toolData[criterion] || toolData[criterion] === 0) {
                                debugLog(`      - 工具 ${toolId} 缺少维度 ${criterion}`);
                                hasIncompleteEvaluation = true;
                                break;
                            }
                        }
                    }

                    if (!hasAnyEvaluation) {
                        debugLog(`  - 方法 ${method.name} 没有任何评分，标记为未开始`);
                        notStarted.push(method.name);
                    } else if (hasIncompleteEvaluation) {
                        debugLog(`  - 方法 ${method.name} 有不完整的评分，标记为部分完成`);
                        incomplete.push(method.name);
                    } else {
                        debugLog(`  - 方法 ${method.name} 评分完整，标记为已完成`);
                        completed.push(method.name);
                    }
                }

                const result = {
                    notStarted,
                    incomplete,
                    completed,
                    totalUnevaluated: notStarted.length + incomplete.length
                };

                debugLog(`\n最终统计结果:`);
                debugLog(`- 未开始: ${notStarted.length} 个`);
                debugLog(`- 部分完成: ${incomplete.length} 个`);
                debugLog(`- 已完成: ${completed.length} 个`);
                debugLog(`- 总未完成: ${result.totalUnevaluated} 个`);

                return result;
            }
        }

        const debugApp = new DebugApp();

        function debugLog(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearDebugLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        function loadCurrentData() {
            const output = document.getElementById('currentDataOutput');
            output.innerHTML = `
                <div class="status info">
                    <h4>当前数据状态:</h4>
                    <p><strong>评分数据方法数:</strong> ${Object.keys(debugApp.evaluationData).length}</p>
                    <p><strong>工具映射方法数:</strong> ${Object.keys(debugApp.methodToolMappings).length}</p>
                    <p><strong>测试方法总数:</strong> ${debugApp.testMethods.length}</p>
                </div>
                <h4>评分数据:</h4>
                <pre>${JSON.stringify(debugApp.evaluationData, null, 2)}</pre>
                <h4>工具映射:</h4>
                <pre>${JSON.stringify(debugApp.methodToolMappings, null, 2)}</pre>
            `;
        }

        function debugEvaluationStatus() {
            clearDebugLog();
            debugLog('开始调试评分状态...');
            
            const status = debugApp.getEvaluationStatus();
            
            const output = document.getElementById('currentDataOutput');
            output.innerHTML += `
                <div class="status success">
                    <h4>评分状态结果:</h4>
                    <p><strong>已完成:</strong> ${status.completed.length} 个 - ${status.completed.join(', ')}</p>
                    <p><strong>部分完成:</strong> ${status.incomplete.length} 个 - ${status.incomplete.join(', ')}</p>
                    <p><strong>未开始:</strong> ${status.notStarted.length} 个 - ${status.notStarted.join(', ')}</p>
                    <p><strong>总未完成:</strong> ${status.totalUnevaluated} 个</p>
                </div>
            `;
        }

        function testStatusFunction() {
            const output = document.getElementById('statusTestOutput');
            
            try {
                const status = debugApp.getEvaluationStatus();
                output.innerHTML = `
                    <div class="status success">
                        <h4>✅ 状态函数测试成功</h4>
                        <pre>${JSON.stringify(status, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                output.innerHTML = `
                    <div class="status error">
                        <h4>❌ 状态函数测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        // 初始化
        debugLog('调试页面已加载');
        loadCurrentData();
    </script>
</body>
</html>
