/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-bottom: 3px solid #5a67d8;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.header h1 {
    font-size: 28px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-stats {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-label {
    font-size: 12px;
    opacity: 0.9;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 20px;
    font-weight: bold;
}

/* Navigation Styles */
.navigation {
    background: #f8f9fa;
    padding: 15px 30px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.nav-btn:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-1px);
}

.nav-btn:disabled {
    background: #adb5bd;
    cursor: not-allowed;
    transform: none;
}

.position-indicator {
    font-weight: 600;
    color: #495057;
    padding: 8px 16px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.nav-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.export-btn {
    background: #28a745;
    color: white;
}

.export-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.reset-btn {
    background: #dc3545;
    color: white;
}

.reset-btn:hover {
    background: #c82333;
    transform: translateY(-1px);
}

/* Main Content */
.main-content {
    padding: 30px;
}

.method-info-panel {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    border-left: 4px solid #667eea;
}

.method-info-panel h2 {
    color: #495057;
    margin-bottom: 8px;
    font-size: 24px;
}

.method-details {
    color: #6c757d;
    font-size: 16px;
}

.method-project {
    font-weight: 600;
    color: #495057;
}

.method-separator {
    margin: 0 8px;
}

.method-name {
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
}

/* Test Cases Container */
.test-cases-container {
    display: grid;
    gap: 30px;
}

.loading-message {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.loading-message i {
    font-size: 48px;
    margin-bottom: 20px;
    color: #667eea;
}

.loading-message p {
    font-size: 18px;
}

/* Test Case Card */
.test-case-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.test-case-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.test-case-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.test-case-title {
    font-size: 18px;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 10px;
}

.method-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.method-badge.method-a { background: #e3f2fd; color: #1976d2; }
.method-badge.method-b { background: #f3e5f5; color: #7b1fa2; }
.method-badge.method-c { background: #e8f5e8; color: #388e3c; }
.method-badge.method-d { background: #fff3e0; color: #f57c00; }

.test-case-content {
    padding: 20px;
}

.code-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 20px;
}

.code-header {
    background: #e9ecef;
    padding: 8px 15px;
    font-size: 12px;
    color: #6c757d;
    border-bottom: 1px solid #dee2e6;
}

.code-content {
    max-height: 400px;
    overflow-y: auto;
}

.code-content pre {
    margin: 0 !important;
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
}

/* Evaluation Panel */
.evaluation-panel {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.evaluation-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.evaluation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.evaluation-item {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.evaluation-item h4 {
    font-size: 14px;
    margin-bottom: 10px;
    color: #495057;
}

.score-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.score-btn {
    background: #e9ecef;
    border: 1px solid #ced4da;
    color: #495057;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    min-width: 35px;
    text-align: center;
}

.score-btn:hover {
    background: #dee2e6;
}

.score-btn.selected {
    background: #667eea;
    color: white;
    border-color: #5a67d8;
}

.total-score {
    margin-top: 10px;
    padding: 8px;
    background: #e3f2fd;
    border-radius: 4px;
    text-align: center;
    font-weight: 600;
    color: #1976d2;
}

/* Evaluation Summary */
.evaluation-summary {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-top: 30px;
    border: 1px solid #e9ecef;
}

.evaluation-summary h3 {
    color: #495057;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.summary-item {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    text-align: center;
}

.summary-label {
    display: block;
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 5px;
}

.summary-value {
    font-size: 24px;
    font-weight: bold;
    color: #495057;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    background: #667eea;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 30px;
}

.criteria-section {
    margin-bottom: 30px;
}

.criteria-section h4 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 18px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.criteria-item {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.criteria-item strong {
    color: #495057;
    display: block;
    margin-bottom: 8px;
}

.criteria-item ul {
    margin-left: 20px;
}

.criteria-item li {
    margin-bottom: 5px;
    color: #6c757d;
}

/* Help Button */
.help-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: #667eea;
    color: white;
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    z-index: 100;
}

.help-btn:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 0;
        box-shadow: none;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .navigation {
        flex-direction: column;
        gap: 15px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .evaluation-grid {
        grid-template-columns: 1fr;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .help-btn {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.test-case-card {
    animation: fadeIn 0.5s ease-out;
}
